/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { combineLatest } from 'rxjs';
import { filter, map, startWith, switchMap, take } from 'rxjs/operators';
import { RedirectCommand } from '../models';
import { isUrlTree } from '../url_tree';
const INITIAL_VALUE = /* @__PURE__ */ Symbol('INITIAL_VALUE');
export function prioritizedGuardValue() {
    return switchMap((obs) => {
        return combineLatest(obs.map((o) => o.pipe(take(1), startWith(INITIAL_VALUE)))).pipe(map((results) => {
            for (const result of results) {
                if (result === true) {
                    // If result is true, check the next one
                    continue;
                }
                else if (result === INITIAL_VALUE) {
                    // If guard has not finished, we need to stop processing.
                    return INITIAL_VALUE;
                }
                else if (result === false || isRedirect(result)) {
                    // Result finished and was not true. Return the result.
                    // Note that we only allow false/UrlTree/RedirectCommand. Other values are considered invalid and
                    // ignored.
                    return result;
                }
            }
            // Everything resolved to true. Return true.
            return true;
        }), filter((item) => item !== INITIAL_VALUE), take(1));
    });
}
function isRedirect(val) {
    return isUrlTree(val) || val instanceof RedirectCommand;
}
//# sourceMappingURL=data:application/json;base64,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