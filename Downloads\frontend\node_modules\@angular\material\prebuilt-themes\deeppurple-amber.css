html{--mat-sys-on-surface: initial}.mat-app-background{background-color:var(--mat-app-background-color, var(--mat-sys-background, transparent));color:var(--mat-app-text-color, var(--mat-sys-on-background, inherit))}.mat-elevation-z0,.mat-mdc-elevation-specific.mat-elevation-z0{box-shadow:var(--mat-app-elevation-shadow-level-0, 0px 0px 0px 0px --mat-sys-shadow, 0px 0px 0px 0px --mat-sys-shadow, 0px 0px 0px 0px --mat-sys-shadow)}.mat-elevation-z1,.mat-mdc-elevation-specific.mat-elevation-z1{box-shadow:var(--mat-app-elevation-shadow-level-1, 0px 2px 1px -1px --mat-sys-shadow, 0px 1px 1px 0px --mat-sys-shadow, 0px 1px 3px 0px --mat-sys-shadow)}.mat-elevation-z2,.mat-mdc-elevation-specific.mat-elevation-z2{box-shadow:var(--mat-app-elevation-shadow-level-2, 0px 3px 1px -2px --mat-sys-shadow, 0px 2px 2px 0px --mat-sys-shadow, 0px 1px 5px 0px --mat-sys-shadow)}.mat-elevation-z3,.mat-mdc-elevation-specific.mat-elevation-z3{box-shadow:var(--mat-app-elevation-shadow-level-3, 0px 3px 3px -2px --mat-sys-shadow, 0px 3px 4px 0px --mat-sys-shadow, 0px 1px 8px 0px --mat-sys-shadow)}.mat-elevation-z4,.mat-mdc-elevation-specific.mat-elevation-z4{box-shadow:var(--mat-app-elevation-shadow-level-4, 0px 2px 4px -1px --mat-sys-shadow, 0px 4px 5px 0px --mat-sys-shadow, 0px 1px 10px 0px --mat-sys-shadow)}.mat-elevation-z5,.mat-mdc-elevation-specific.mat-elevation-z5{box-shadow:var(--mat-app-elevation-shadow-level-5, 0px 3px 5px -1px --mat-sys-shadow, 0px 5px 8px 0px --mat-sys-shadow, 0px 1px 14px 0px --mat-sys-shadow)}.mat-elevation-z6,.mat-mdc-elevation-specific.mat-elevation-z6{box-shadow:var(--mat-app-elevation-shadow-level-6, 0px 3px 5px -1px --mat-sys-shadow, 0px 6px 10px 0px --mat-sys-shadow, 0px 1px 18px 0px --mat-sys-shadow)}.mat-elevation-z7,.mat-mdc-elevation-specific.mat-elevation-z7{box-shadow:var(--mat-app-elevation-shadow-level-7, 0px 4px 5px -2px --mat-sys-shadow, 0px 7px 10px 1px --mat-sys-shadow, 0px 2px 16px 1px --mat-sys-shadow)}.mat-elevation-z8,.mat-mdc-elevation-specific.mat-elevation-z8{box-shadow:var(--mat-app-elevation-shadow-level-8, 0px 5px 5px -3px --mat-sys-shadow, 0px 8px 10px 1px --mat-sys-shadow, 0px 3px 14px 2px --mat-sys-shadow)}.mat-elevation-z9,.mat-mdc-elevation-specific.mat-elevation-z9{box-shadow:var(--mat-app-elevation-shadow-level-9, 0px 5px 6px -3px --mat-sys-shadow, 0px 9px 12px 1px --mat-sys-shadow, 0px 3px 16px 2px --mat-sys-shadow)}.mat-elevation-z10,.mat-mdc-elevation-specific.mat-elevation-z10{box-shadow:var(--mat-app-elevation-shadow-level-10, 0px 6px 6px -3px --mat-sys-shadow, 0px 10px 14px 1px --mat-sys-shadow, 0px 4px 18px 3px --mat-sys-shadow)}.mat-elevation-z11,.mat-mdc-elevation-specific.mat-elevation-z11{box-shadow:var(--mat-app-elevation-shadow-level-11, 0px 6px 7px -4px --mat-sys-shadow, 0px 11px 15px 1px --mat-sys-shadow, 0px 4px 20px 3px --mat-sys-shadow)}.mat-elevation-z12,.mat-mdc-elevation-specific.mat-elevation-z12{box-shadow:var(--mat-app-elevation-shadow-level-12, 0px 7px 8px -4px --mat-sys-shadow, 0px 12px 17px 2px --mat-sys-shadow, 0px 5px 22px 4px --mat-sys-shadow)}.mat-elevation-z13,.mat-mdc-elevation-specific.mat-elevation-z13{box-shadow:var(--mat-app-elevation-shadow-level-13, 0px 7px 8px -4px --mat-sys-shadow, 0px 13px 19px 2px --mat-sys-shadow, 0px 5px 24px 4px --mat-sys-shadow)}.mat-elevation-z14,.mat-mdc-elevation-specific.mat-elevation-z14{box-shadow:var(--mat-app-elevation-shadow-level-14, 0px 7px 9px -4px --mat-sys-shadow, 0px 14px 21px 2px --mat-sys-shadow, 0px 5px 26px 4px --mat-sys-shadow)}.mat-elevation-z15,.mat-mdc-elevation-specific.mat-elevation-z15{box-shadow:var(--mat-app-elevation-shadow-level-15, 0px 8px 9px -5px --mat-sys-shadow, 0px 15px 22px 2px --mat-sys-shadow, 0px 6px 28px 5px --mat-sys-shadow)}.mat-elevation-z16,.mat-mdc-elevation-specific.mat-elevation-z16{box-shadow:var(--mat-app-elevation-shadow-level-16, 0px 8px 10px -5px --mat-sys-shadow, 0px 16px 24px 2px --mat-sys-shadow, 0px 6px 30px 5px --mat-sys-shadow)}.mat-elevation-z17,.mat-mdc-elevation-specific.mat-elevation-z17{box-shadow:var(--mat-app-elevation-shadow-level-17, 0px 8px 11px -5px --mat-sys-shadow, 0px 17px 26px 2px --mat-sys-shadow, 0px 6px 32px 5px --mat-sys-shadow)}.mat-elevation-z18,.mat-mdc-elevation-specific.mat-elevation-z18{box-shadow:var(--mat-app-elevation-shadow-level-18, 0px 9px 11px -5px --mat-sys-shadow, 0px 18px 28px 2px --mat-sys-shadow, 0px 7px 34px 6px --mat-sys-shadow)}.mat-elevation-z19,.mat-mdc-elevation-specific.mat-elevation-z19{box-shadow:var(--mat-app-elevation-shadow-level-19, 0px 9px 12px -6px --mat-sys-shadow, 0px 19px 29px 2px --mat-sys-shadow, 0px 7px 36px 6px --mat-sys-shadow)}.mat-elevation-z20,.mat-mdc-elevation-specific.mat-elevation-z20{box-shadow:var(--mat-app-elevation-shadow-level-20, 0px 10px 13px -6px --mat-sys-shadow, 0px 20px 31px 3px --mat-sys-shadow, 0px 8px 38px 7px --mat-sys-shadow)}.mat-elevation-z21,.mat-mdc-elevation-specific.mat-elevation-z21{box-shadow:var(--mat-app-elevation-shadow-level-21, 0px 10px 13px -6px --mat-sys-shadow, 0px 21px 33px 3px --mat-sys-shadow, 0px 8px 40px 7px --mat-sys-shadow)}.mat-elevation-z22,.mat-mdc-elevation-specific.mat-elevation-z22{box-shadow:var(--mat-app-elevation-shadow-level-22, 0px 10px 14px -6px --mat-sys-shadow, 0px 22px 35px 3px --mat-sys-shadow, 0px 8px 42px 7px --mat-sys-shadow)}.mat-elevation-z23,.mat-mdc-elevation-specific.mat-elevation-z23{box-shadow:var(--mat-app-elevation-shadow-level-23, 0px 11px 14px -7px --mat-sys-shadow, 0px 23px 36px 3px --mat-sys-shadow, 0px 9px 44px 8px --mat-sys-shadow)}.mat-elevation-z24,.mat-mdc-elevation-specific.mat-elevation-z24{box-shadow:var(--mat-app-elevation-shadow-level-24, 0px 11px 15px -7px --mat-sys-shadow, 0px 24px 38px 3px --mat-sys-shadow, 0px 9px 46px 8px --mat-sys-shadow)}html{--mat-app-background-color: #fafafa;--mat-app-text-color: rgba(0, 0, 0, 0.87);--mat-app-elevation-shadow-level-0: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-1: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-2: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-3: 0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-4: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-5: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-6: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-7: 0px 4px 5px -2px rgba(0, 0, 0, 0.2), 0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-8: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-9: 0px 5px 6px -3px rgba(0, 0, 0, 0.2), 0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-10: 0px 6px 6px -3px rgba(0, 0, 0, 0.2), 0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-11: 0px 6px 7px -4px rgba(0, 0, 0, 0.2), 0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-12: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-13: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-14: 0px 7px 9px -4px rgba(0, 0, 0, 0.2), 0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-15: 0px 8px 9px -5px rgba(0, 0, 0, 0.2), 0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-16: 0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-17: 0px 8px 11px -5px rgba(0, 0, 0, 0.2), 0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-18: 0px 9px 11px -5px rgba(0, 0, 0, 0.2), 0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-19: 0px 9px 12px -6px rgba(0, 0, 0, 0.2), 0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-20: 0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-21: 0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-22: 0px 10px 14px -6px rgba(0, 0, 0, 0.2), 0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-23: 0px 11px 14px -7px rgba(0, 0, 0, 0.2), 0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-24: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)}html{--mat-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent)}html{--mat-option-selected-state-label-text-color: #673ab7;--mat-option-label-text-color: rgba(0, 0, 0, 0.87);--mat-option-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-option-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-option-selected-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent)}.mat-accent{--mat-option-selected-state-label-text-color: #ffd740;--mat-option-label-text-color: rgba(0, 0, 0, 0.87);--mat-option-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-option-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-option-selected-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent)}.mat-warn{--mat-option-selected-state-label-text-color: #f44336;--mat-option-label-text-color: rgba(0, 0, 0, 0.87);--mat-option-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-option-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-option-selected-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent)}html{--mat-optgroup-label-text-color: rgba(0, 0, 0, 0.87)}html{--mat-pseudo-checkbox-full-selected-icon-color: #ffd740;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-full-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-minimal-selected-checkmark-color: #ffd740;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}.mat-primary{--mat-pseudo-checkbox-full-selected-icon-color: #673ab7;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-full-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-minimal-selected-checkmark-color: #673ab7;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}.mat-accent{--mat-pseudo-checkbox-full-selected-icon-color: #ffd740;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-full-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-minimal-selected-checkmark-color: #ffd740;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}.mat-warn{--mat-pseudo-checkbox-full-selected-icon-color: #f44336;--mat-pseudo-checkbox-full-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color: #fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-full-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-pseudo-checkbox-minimal-selected-checkmark-color: #f44336;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}html{--mat-option-label-text-font: Roboto, sans-serif;--mat-option-label-text-line-height: 24px;--mat-option-label-text-size: 16px;--mat-option-label-text-tracking: 0.03125em;--mat-option-label-text-weight: 400}html{--mat-optgroup-label-text-font: Roboto, sans-serif;--mat-optgroup-label-text-line-height: 24px;--mat-optgroup-label-text-size: 16px;--mat-optgroup-label-text-tracking: 0.03125em;--mat-optgroup-label-text-weight: 400}html{--mat-card-elevated-container-shape: 4px;--mat-card-outlined-container-shape: 4px;--mat-card-filled-container-shape: 4px;--mat-card-outlined-outline-width: 1px}html{--mat-card-elevated-container-color: white;--mat-card-elevated-container-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mat-card-outlined-container-color: white;--mat-card-outlined-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-card-outlined-outline-color: rgba(0, 0, 0, 0.12);--mat-card-subtitle-text-color: rgba(0, 0, 0, 0.54);--mat-card-filled-container-color: white;--mat-card-filled-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}html{--mat-card-title-text-font: Roboto, sans-serif;--mat-card-title-text-line-height: 32px;--mat-card-title-text-size: 20px;--mat-card-title-text-tracking: 0.0125em;--mat-card-title-text-weight: 500;--mat-card-subtitle-text-font: Roboto, sans-serif;--mat-card-subtitle-text-line-height: 22px;--mat-card-subtitle-text-size: 14px;--mat-card-subtitle-text-tracking: 0.0071428571em;--mat-card-subtitle-text-weight: 500}html{--mat-progress-bar-active-indicator-height: 4px;--mat-progress-bar-track-height: 4px;--mat-progress-bar-track-shape: 0}.mat-mdc-progress-bar{--mat-progress-bar-active-indicator-color: #673ab7;--mat-progress-bar-track-color: rgba(103, 58, 183, 0.25)}.mat-mdc-progress-bar.mat-accent{--mat-progress-bar-active-indicator-color: #ffd740;--mat-progress-bar-track-color: rgba(255, 215, 64, 0.25)}.mat-mdc-progress-bar.mat-warn{--mat-progress-bar-active-indicator-color: #f44336;--mat-progress-bar-track-color: rgba(244, 67, 54, 0.25)}html{--mat-tooltip-container-shape: 4px;--mat-tooltip-supporting-text-line-height: 16px}html{--mat-tooltip-container-color: #424242;--mat-tooltip-supporting-text-color: white}html{--mat-tooltip-supporting-text-font: Roboto, sans-serif;--mat-tooltip-supporting-text-size: 12px;--mat-tooltip-supporting-text-weight: 400;--mat-tooltip-supporting-text-tracking: 0.0333333333em}html{--mat-form-field-filled-active-indicator-height: 1px;--mat-form-field-filled-focus-active-indicator-height: 2px;--mat-form-field-filled-container-shape: 4px;--mat-form-field-outlined-outline-width: 1px;--mat-form-field-outlined-focus-outline-width: 2px;--mat-form-field-outlined-container-shape: 4px}html{--mat-form-field-focus-select-arrow-color: color-mix(in srgb, #673ab7 87%, transparent);--mat-form-field-filled-caret-color: #673ab7;--mat-form-field-filled-focus-active-indicator-color: #673ab7;--mat-form-field-filled-focus-label-text-color: color-mix(in srgb, #673ab7 87%, transparent);--mat-form-field-outlined-caret-color: #673ab7;--mat-form-field-outlined-focus-outline-color: #673ab7;--mat-form-field-outlined-focus-label-text-color: color-mix(in srgb, #673ab7 87%, transparent);--mat-form-field-disabled-input-text-placeholder-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-state-layer-color: rgba(0, 0, 0, 0.87);--mat-form-field-error-text-color: #f44336;--mat-form-field-select-option-text-color: inherit;--mat-form-field-select-disabled-option-text-color: GrayText;--mat-form-field-leading-icon-color: unset;--mat-form-field-disabled-leading-icon-color: unset;--mat-form-field-trailing-icon-color: unset;--mat-form-field-disabled-trailing-icon-color: unset;--mat-form-field-error-focus-trailing-icon-color: unset;--mat-form-field-error-hover-trailing-icon-color: unset;--mat-form-field-error-trailing-icon-color: unset;--mat-form-field-enabled-select-arrow-color: rgba(0, 0, 0, 0.54);--mat-form-field-disabled-select-arrow-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-hover-state-layer-opacity: 0.04;--mat-form-field-focus-state-layer-opacity: 0.12;--mat-form-field-filled-container-color: #f6f6f6;--mat-form-field-filled-disabled-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-form-field-filled-label-text-color: rgba(0, 0, 0, 0.54);--mat-form-field-filled-hover-label-text-color: rgba(0, 0, 0, 0.54);--mat-form-field-filled-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-filled-input-text-color: rgba(0, 0, 0, 0.87);--mat-form-field-filled-disabled-input-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-filled-input-text-placeholder-color: rgba(0, 0, 0, 0.54);--mat-form-field-filled-error-hover-label-text-color: #f44336;--mat-form-field-filled-error-focus-label-text-color: #f44336;--mat-form-field-filled-error-label-text-color: #f44336;--mat-form-field-filled-error-caret-color: #f44336;--mat-form-field-filled-active-indicator-color: rgba(0, 0, 0, 0.54);--mat-form-field-filled-disabled-active-indicator-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-form-field-filled-hover-active-indicator-color: rgba(0, 0, 0, 0.87);--mat-form-field-filled-error-active-indicator-color: #f44336;--mat-form-field-filled-error-focus-active-indicator-color: #f44336;--mat-form-field-filled-error-hover-active-indicator-color: #f44336;--mat-form-field-outlined-label-text-color: rgba(0, 0, 0, 0.54);--mat-form-field-outlined-hover-label-text-color: rgba(0, 0, 0, 0.87);--mat-form-field-outlined-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-outlined-input-text-color: rgba(0, 0, 0, 0.87);--mat-form-field-outlined-disabled-input-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-form-field-outlined-input-text-placeholder-color: rgba(0, 0, 0, 0.54);--mat-form-field-outlined-error-caret-color: #f44336;--mat-form-field-outlined-error-focus-label-text-color: #f44336;--mat-form-field-outlined-error-label-text-color: #f44336;--mat-form-field-outlined-error-hover-label-text-color: #f44336;--mat-form-field-outlined-outline-color: rgba(0, 0, 0, 0.38);--mat-form-field-outlined-disabled-outline-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-form-field-outlined-hover-outline-color: rgba(0, 0, 0, 0.87);--mat-form-field-outlined-error-focus-outline-color: #f44336;--mat-form-field-outlined-error-hover-outline-color: #f44336;--mat-form-field-outlined-error-outline-color: #f44336}.mat-mdc-form-field.mat-accent{--mat-form-field-focus-select-arrow-color: color-mix(in srgb, #ffd740 87%, transparent);--mat-form-field-filled-caret-color: #ffd740;--mat-form-field-filled-focus-active-indicator-color: #ffd740;--mat-form-field-filled-focus-label-text-color: color-mix(in srgb, #ffd740 87%, transparent);--mat-form-field-outlined-caret-color: #ffd740;--mat-form-field-outlined-focus-outline-color: #ffd740;--mat-form-field-outlined-focus-label-text-color: color-mix(in srgb, #ffd740 87%, transparent)}.mat-mdc-form-field.mat-warn{--mat-form-field-focus-select-arrow-color: color-mix(in srgb, #f44336 87%, transparent);--mat-form-field-filled-caret-color: #f44336;--mat-form-field-filled-focus-active-indicator-color: #f44336;--mat-form-field-filled-focus-label-text-color: color-mix(in srgb, #f44336 87%, transparent);--mat-form-field-outlined-caret-color: #f44336;--mat-form-field-outlined-focus-outline-color: #f44336;--mat-form-field-outlined-focus-label-text-color: color-mix(in srgb, #f44336 87%, transparent)}html{--mat-form-field-container-height: 56px;--mat-form-field-filled-label-display: block;--mat-form-field-container-vertical-padding: 16px;--mat-form-field-filled-with-label-container-padding-top: 24px;--mat-form-field-filled-with-label-container-padding-bottom: 8px}html{--mat-form-field-container-text-font: Roboto, sans-serif;--mat-form-field-container-text-line-height: 24px;--mat-form-field-container-text-size: 16px;--mat-form-field-container-text-tracking: 0.03125em;--mat-form-field-container-text-weight: 400;--mat-form-field-outlined-label-text-populated-size: 16px;--mat-form-field-subscript-text-font: Roboto, sans-serif;--mat-form-field-subscript-text-line-height: 20px;--mat-form-field-subscript-text-size: 12px;--mat-form-field-subscript-text-tracking: 0.0333333333em;--mat-form-field-subscript-text-weight: 400;--mat-form-field-filled-label-text-font: Roboto, sans-serif;--mat-form-field-filled-label-text-size: 16px;--mat-form-field-filled-label-text-tracking: 0.03125em;--mat-form-field-filled-label-text-weight: 400;--mat-form-field-outlined-label-text-font: Roboto, sans-serif;--mat-form-field-outlined-label-text-size: 16px;--mat-form-field-outlined-label-text-tracking: 0.03125em;--mat-form-field-outlined-label-text-weight: 400}html{--mat-select-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-placeholder-text-color: rgba(0, 0, 0, 0.54);--mat-select-enabled-arrow-color: rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-focused-arrow-color: #673ab7;--mat-select-invalid-arrow-color: #f44336}.mat-mdc-form-field.mat-accent{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-placeholder-text-color: rgba(0, 0, 0, 0.54);--mat-select-enabled-arrow-color: rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-focused-arrow-color: #ffd740;--mat-select-invalid-arrow-color: #f44336}.mat-mdc-form-field.mat-warn{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-placeholder-text-color: rgba(0, 0, 0, 0.54);--mat-select-enabled-arrow-color: rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-select-focused-arrow-color: #f44336;--mat-select-invalid-arrow-color: #f44336}html{--mat-select-arrow-transform: translateY(-8px)}html{--mat-select-trigger-text-font: Roboto, sans-serif;--mat-select-trigger-text-line-height: 24px;--mat-select-trigger-text-size: 16px;--mat-select-trigger-text-tracking: 0.03125em;--mat-select-trigger-text-weight: 400}html{--mat-autocomplete-container-shape: 4px;--mat-autocomplete-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-autocomplete-background-color: white}html{--mat-dialog-container-shape: 4px;--mat-dialog-container-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mat-dialog-container-max-width: 80vw;--mat-dialog-container-small-max-width: 80vw;--mat-dialog-container-min-width: 0;--mat-dialog-actions-alignment: start;--mat-dialog-actions-padding: 8px;--mat-dialog-content-padding: 20px 24px;--mat-dialog-with-actions-content-padding: 20px 24px;--mat-dialog-headline-padding: 0 24px 9px}html{--mat-dialog-container-color: white;--mat-dialog-subhead-color: rgba(0, 0, 0, 0.87);--mat-dialog-supporting-text-color: rgba(0, 0, 0, 0.54)}html{--mat-dialog-subhead-font: Roboto, sans-serif;--mat-dialog-subhead-line-height: 32px;--mat-dialog-subhead-size: 20px;--mat-dialog-subhead-weight: 500;--mat-dialog-subhead-tracking: 0.0125em;--mat-dialog-supporting-text-font: Roboto, sans-serif;--mat-dialog-supporting-text-line-height: 24px;--mat-dialog-supporting-text-size: 16px;--mat-dialog-supporting-text-weight: 400;--mat-dialog-supporting-text-tracking: 0.03125em}.mat-mdc-standard-chip{--mat-chip-container-shape-radius: 16px;--mat-chip-disabled-container-opacity: 0.4;--mat-chip-disabled-outline-color: transparent;--mat-chip-flat-selected-outline-width: 0;--mat-chip-focus-outline-color: transparent;--mat-chip-hover-state-layer-opacity: 0.04;--mat-chip-outline-color: transparent;--mat-chip-outline-width: 0;--mat-chip-selected-hover-state-layer-opacity: 0.04;--mat-chip-selected-trailing-action-state-layer-color: transparent;--mat-chip-trailing-action-focus-opacity: 1;--mat-chip-trailing-action-focus-state-layer-opacity: 0;--mat-chip-trailing-action-hover-state-layer-opacity: 0;--mat-chip-trailing-action-opacity: 0.54;--mat-chip-trailing-action-state-layer-color: transparent;--mat-chip-with-avatar-avatar-shape-radius: 14px;--mat-chip-with-avatar-avatar-size: 28px;--mat-chip-with-avatar-disabled-avatar-opacity: 1;--mat-chip-with-icon-disabled-icon-opacity: 1;--mat-chip-with-icon-icon-size: 18px;--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity: 1}.mat-mdc-standard-chip{--mat-chip-disabled-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-elevated-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-chip-elevated-disabled-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-chip-elevated-selected-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-chip-flat-disabled-selected-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-chip-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-focus-state-layer-opacity: 0.12;--mat-chip-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-disabled-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-focus-state-layer-color: 0.12;--mat-chip-selected-focus-state-layer-opacity: 0.12;--mat-chip-selected-hover-state-layer-color: 0.04;--mat-chip-selected-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-disabled-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-trailing-icon-disabled-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-trailing-icon-trailing-icon-color: rgba(0, 0, 0, 0.87)}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary{--mat-chip-disabled-label-text-color: white;--mat-chip-elevated-container-color: #673ab7;--mat-chip-elevated-disabled-container-color: #673ab7;--mat-chip-elevated-selected-container-color: #673ab7;--mat-chip-flat-disabled-selected-container-color: #673ab7;--mat-chip-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-focus-state-layer-opacity: 0.12;--mat-chip-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-label-text-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-focus-state-layer-color: 0.12;--mat-chip-selected-focus-state-layer-opacity: 0.12;--mat-chip-selected-hover-state-layer-color: 0.04;--mat-chip-selected-label-text-color: white;--mat-chip-selected-trailing-icon-color: white;--mat-chip-with-icon-disabled-icon-color: white;--mat-chip-with-icon-icon-color: white;--mat-chip-with-icon-selected-icon-color: white;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mat-chip-with-trailing-icon-trailing-icon-color: white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-accent,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-accent{--mat-chip-disabled-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-elevated-container-color: #ffd740;--mat-chip-elevated-disabled-container-color: #ffd740;--mat-chip-elevated-selected-container-color: #ffd740;--mat-chip-flat-disabled-selected-container-color: #ffd740;--mat-chip-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-focus-state-layer-opacity: 0.12;--mat-chip-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-disabled-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-focus-state-layer-color: 0.12;--mat-chip-selected-focus-state-layer-opacity: 0.12;--mat-chip-selected-hover-state-layer-color: 0.04;--mat-chip-selected-label-text-color: rgba(0, 0, 0, 0.87);--mat-chip-selected-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-disabled-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-icon-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-trailing-icon-disabled-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-chip-with-trailing-icon-trailing-icon-color: rgba(0, 0, 0, 0.87)}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-warn,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-warn{--mat-chip-disabled-label-text-color: white;--mat-chip-elevated-container-color: #f44336;--mat-chip-elevated-disabled-container-color: #f44336;--mat-chip-elevated-selected-container-color: #f44336;--mat-chip-flat-disabled-selected-container-color: #f44336;--mat-chip-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-focus-state-layer-opacity: 0.12;--mat-chip-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-chip-label-text-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-focus-state-layer-color: 0.12;--mat-chip-selected-focus-state-layer-opacity: 0.12;--mat-chip-selected-hover-state-layer-color: 0.04;--mat-chip-selected-label-text-color: white;--mat-chip-selected-trailing-icon-color: white;--mat-chip-with-icon-disabled-icon-color: white;--mat-chip-with-icon-icon-color: white;--mat-chip-with-icon-selected-icon-color: white;--mat-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mat-chip-with-trailing-icon-trailing-icon-color: white}.mat-mdc-chip.mat-mdc-standard-chip{--mat-chip-container-height: 32px}.mat-mdc-standard-chip{--mat-chip-label-text-font: Roboto, sans-serif;--mat-chip-label-text-line-height: 20px;--mat-chip-label-text-size: 14px;--mat-chip-label-text-tracking: 0.0178571429em;--mat-chip-label-text-weight: 400}html{--mat-slide-toggle-disabled-handle-opacity: 0.38;--mat-slide-toggle-disabled-selected-handle-opacity: 0.38;--mat-slide-toggle-disabled-selected-icon-opacity: 0.38;--mat-slide-toggle-disabled-track-opacity: 0.12;--mat-slide-toggle-disabled-unselected-handle-opacity: 0.38;--mat-slide-toggle-disabled-unselected-icon-opacity: 0.38;--mat-slide-toggle-disabled-unselected-track-outline-color: transparent;--mat-slide-toggle-disabled-unselected-track-outline-width: 1px;--mat-slide-toggle-handle-height: 20px;--mat-slide-toggle-handle-shape: 10px;--mat-slide-toggle-handle-width: 20px;--mat-slide-toggle-hidden-track-opacity: 1;--mat-slide-toggle-hidden-track-transition: transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);--mat-slide-toggle-pressed-handle-size: 20px;--mat-slide-toggle-selected-focus-state-layer-opacity: 0.12;--mat-slide-toggle-selected-handle-horizontal-margin: 0;--mat-slide-toggle-selected-handle-size: 20px;--mat-slide-toggle-selected-hover-state-layer-opacity: 0.04;--mat-slide-toggle-selected-icon-size: 18px;--mat-slide-toggle-selected-pressed-handle-horizontal-margin: 0;--mat-slide-toggle-selected-pressed-state-layer-opacity: 0.12;--mat-slide-toggle-selected-track-outline-color: transparent;--mat-slide-toggle-selected-track-outline-width: 1px;--mat-slide-toggle-selected-with-icon-handle-horizontal-margin: 0;--mat-slide-toggle-track-height: 14px;--mat-slide-toggle-track-outline-color: transparent;--mat-slide-toggle-track-outline-width: 1px;--mat-slide-toggle-track-shape: 7px;--mat-slide-toggle-track-width: 36px;--mat-slide-toggle-unselected-focus-state-layer-opacity: 0.12;--mat-slide-toggle-unselected-handle-horizontal-margin: 0;--mat-slide-toggle-unselected-handle-size: 20px;--mat-slide-toggle-unselected-hover-state-layer-opacity: 0.12;--mat-slide-toggle-unselected-icon-size: 18px;--mat-slide-toggle-unselected-pressed-handle-horizontal-margin: 0;--mat-slide-toggle-unselected-pressed-state-layer-opacity: 0.1;--mat-slide-toggle-unselected-with-icon-handle-horizontal-margin: 0;--mat-slide-toggle-visible-track-opacity: 1;--mat-slide-toggle-visible-track-transition: transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);--mat-slide-toggle-with-icon-handle-size: 20px}html{--mat-slide-toggle-selected-icon-color: white;--mat-slide-toggle-disabled-selected-icon-color: white;--mat-slide-toggle-selected-focus-state-layer-color: #673ab7;--mat-slide-toggle-selected-handle-color: #673ab7;--mat-slide-toggle-selected-hover-state-layer-color: #673ab7;--mat-slide-toggle-selected-pressed-state-layer-color: #673ab7;--mat-slide-toggle-selected-focus-handle-color: #673ab7;--mat-slide-toggle-selected-hover-handle-color: #673ab7;--mat-slide-toggle-selected-pressed-handle-color: #673ab7;--mat-slide-toggle-selected-focus-track-color: #9575cd;--mat-slide-toggle-selected-hover-track-color: #9575cd;--mat-slide-toggle-selected-pressed-track-color: #9575cd;--mat-slide-toggle-selected-track-color: #9575cd;--mat-slide-toggle-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-slide-toggle-disabled-handle-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-slide-toggle-disabled-selected-handle-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-disabled-selected-track-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-disabled-unselected-handle-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-disabled-unselected-icon-color: #f6f6f6;--mat-slide-toggle-disabled-unselected-track-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-handle-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mat-slide-toggle-handle-surface-color: white;--mat-slide-toggle-label-text-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-unselected-hover-handle-color: #424242;--mat-slide-toggle-unselected-focus-handle-color: #424242;--mat-slide-toggle-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-unselected-focus-track-color: rgba(0, 0, 0, 0.12);--mat-slide-toggle-unselected-icon-color: #f6f6f6;--mat-slide-toggle-unselected-handle-color: rgba(0, 0, 0, 0.54);--mat-slide-toggle-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-unselected-hover-track-color: rgba(0, 0, 0, 0.12);--mat-slide-toggle-unselected-pressed-handle-color: #424242;--mat-slide-toggle-unselected-pressed-track-color: rgba(0, 0, 0, 0.12);--mat-slide-toggle-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-unselected-track-color: rgba(0, 0, 0, 0.12)}.mat-mdc-slide-toggle.mat-accent{--mat-slide-toggle-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-slide-toggle-selected-focus-state-layer-color: #ffd740;--mat-slide-toggle-selected-handle-color: #ffd740;--mat-slide-toggle-selected-hover-state-layer-color: #ffd740;--mat-slide-toggle-selected-pressed-state-layer-color: #ffd740;--mat-slide-toggle-selected-focus-handle-color: #ffd740;--mat-slide-toggle-selected-hover-handle-color: #ffd740;--mat-slide-toggle-selected-pressed-handle-color: #ffd740;--mat-slide-toggle-selected-focus-track-color: #ffd54f;--mat-slide-toggle-selected-hover-track-color: #ffd54f;--mat-slide-toggle-selected-pressed-track-color: #ffd54f;--mat-slide-toggle-selected-track-color: #ffd54f}.mat-mdc-slide-toggle.mat-warn{--mat-slide-toggle-selected-icon-color: white;--mat-slide-toggle-disabled-selected-icon-color: white;--mat-slide-toggle-selected-focus-state-layer-color: #f44336;--mat-slide-toggle-selected-handle-color: #f44336;--mat-slide-toggle-selected-hover-state-layer-color: #f44336;--mat-slide-toggle-selected-pressed-state-layer-color: #f44336;--mat-slide-toggle-selected-focus-handle-color: #f44336;--mat-slide-toggle-selected-hover-handle-color: #f44336;--mat-slide-toggle-selected-pressed-handle-color: #f44336;--mat-slide-toggle-selected-focus-track-color: #e57373;--mat-slide-toggle-selected-hover-track-color: #e57373;--mat-slide-toggle-selected-pressed-track-color: #e57373;--mat-slide-toggle-selected-track-color: #e57373}html{--mat-slide-toggle-state-layer-size: 40px}html{--mat-slide-toggle-label-text-font: Roboto, sans-serif;--mat-slide-toggle-label-text-line-height: 20px;--mat-slide-toggle-label-text-size: 14px;--mat-slide-toggle-label-text-tracking: 0.0178571429em;--mat-slide-toggle-label-text-weight: 400}html .mat-mdc-slide-toggle{--mat-slide-toggle-label-text-font: Roboto, sans-serif;--mat-slide-toggle-label-text-line-height: 20px;--mat-slide-toggle-label-text-size: 14px;--mat-slide-toggle-label-text-tracking: 0.0178571429em;--mat-slide-toggle-label-text-weight: 400}html{--mat-radio-disabled-selected-icon-opacity: 0.38;--mat-radio-disabled-unselected-icon-opacity: 0.38;--mat-radio-state-layer-size: 40px}.mat-mdc-radio-button.mat-primary{--mat-radio-checked-ripple-color: #673ab7;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #673ab7;--mat-radio-selected-hover-icon-color: #673ab7;--mat-radio-selected-icon-color: #673ab7;--mat-radio-selected-pressed-icon-color: #673ab7;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}.mat-mdc-radio-button.mat-accent{--mat-radio-checked-ripple-color: #ffd740;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #ffd740;--mat-radio-selected-hover-icon-color: #ffd740;--mat-radio-selected-icon-color: #ffd740;--mat-radio-selected-pressed-icon-color: #ffd740;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}.mat-mdc-radio-button.mat-warn{--mat-radio-checked-ripple-color: #f44336;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #f44336;--mat-radio-selected-hover-icon-color: #f44336;--mat-radio-selected-icon-color: #f44336;--mat-radio-selected-pressed-icon-color: #f44336;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}html{--mat-radio-state-layer-size: 40px;--mat-radio-touch-target-display: block}html{--mat-radio-label-text-font: Roboto, sans-serif;--mat-radio-label-text-line-height: 20px;--mat-radio-label-text-size: 14px;--mat-radio-label-text-tracking: 0.0178571429em;--mat-radio-label-text-weight: 400}html{--mat-slider-active-track-height: 6px;--mat-slider-active-track-shape: 9999px;--mat-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mat-slider-handle-height: 20px;--mat-slider-handle-shape: 50%;--mat-slider-handle-width: 20px;--mat-slider-inactive-track-height: 4px;--mat-slider-inactive-track-shape: 9999px;--mat-slider-value-indicator-border-radius: 4px;--mat-slider-value-indicator-caret-display: block;--mat-slider-value-indicator-container-transform: translateX(-50%);--mat-slider-value-indicator-height: 32px;--mat-slider-value-indicator-padding: 0 12px;--mat-slider-value-indicator-text-transform: none;--mat-slider-value-indicator-width: auto;--mat-slider-with-overlap-handle-outline-width: 1px;--mat-slider-with-tick-marks-active-container-opacity: 0.6;--mat-slider-with-tick-marks-container-shape: 50%;--mat-slider-with-tick-marks-container-size: 2px;--mat-slider-with-tick-marks-inactive-container-opacity: 0.6}html{--mat-slider-active-track-color: #673ab7;--mat-slider-focus-handle-color: #673ab7;--mat-slider-handle-color: #673ab7;--mat-slider-hover-handle-color: #673ab7;--mat-slider-focus-state-layer-color: color-mix(in srgb, #673ab7 12%, transparent);--mat-slider-hover-state-layer-color: color-mix(in srgb, #673ab7 4%, transparent);--mat-slider-inactive-track-color: #673ab7;--mat-slider-ripple-color: #673ab7;--mat-slider-with-tick-marks-active-container-color: white;--mat-slider-with-tick-marks-inactive-container-color: #673ab7;--mat-slider-disabled-active-track-color: rgba(0, 0, 0, 0.87);--mat-slider-disabled-handle-color: rgba(0, 0, 0, 0.87);--mat-slider-disabled-inactive-track-color: rgba(0, 0, 0, 0.87);--mat-slider-label-container-color: #424242;--mat-slider-label-label-text-color: white;--mat-slider-value-indicator-opacity: 1;--mat-slider-with-overlap-handle-outline-color: rgba(0, 0, 0, 0.87);--mat-slider-with-tick-marks-disabled-container-color: rgba(0, 0, 0, 0.87)}.mat-accent{--mat-slider-active-track-color: #ffd740;--mat-slider-focus-handle-color: #ffd740;--mat-slider-handle-color: #ffd740;--mat-slider-hover-handle-color: #ffd740;--mat-slider-focus-state-layer-color: color-mix(in srgb, #ffd740 12%, transparent);--mat-slider-hover-state-layer-color: color-mix(in srgb, #ffd740 4%, transparent);--mat-slider-inactive-track-color: #ffd740;--mat-slider-ripple-color: #ffd740;--mat-slider-with-tick-marks-active-container-color: rgba(0, 0, 0, 0.87);--mat-slider-with-tick-marks-inactive-container-color: #ffd740}.mat-warn{--mat-slider-active-track-color: #f44336;--mat-slider-focus-handle-color: #f44336;--mat-slider-handle-color: #f44336;--mat-slider-hover-handle-color: #f44336;--mat-slider-focus-state-layer-color: color-mix(in srgb, #f44336 12%, transparent);--mat-slider-hover-state-layer-color: color-mix(in srgb, #f44336 4%, transparent);--mat-slider-inactive-track-color: #f44336;--mat-slider-ripple-color: #f44336;--mat-slider-with-tick-marks-active-container-color: white;--mat-slider-with-tick-marks-inactive-container-color: #f44336}html{--mat-slider-label-label-text-font: Roboto, sans-serif;--mat-slider-label-label-text-size: 14px;--mat-slider-label-label-text-line-height: 22px;--mat-slider-label-label-text-tracking: 0.0071428571em;--mat-slider-label-label-text-weight: 500}html{--mat-menu-container-shape: 4px;--mat-menu-divider-bottom-spacing: 0;--mat-menu-divider-top-spacing: 0;--mat-menu-item-spacing: 16px;--mat-menu-item-icon-size: 24px;--mat-menu-item-leading-spacing: 16px;--mat-menu-item-trailing-spacing: 16px;--mat-menu-item-with-icon-leading-spacing: 16px;--mat-menu-item-with-icon-trailing-spacing: 16px;--mat-menu-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-menu-item-label-text-color: rgba(0, 0, 0, 0.87);--mat-menu-item-icon-color: rgba(0, 0, 0, 0.87);--mat-menu-item-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-menu-item-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-menu-container-color: white;--mat-menu-divider-color: rgba(0, 0, 0, 0.12)}html{--mat-menu-item-label-text-font: Roboto, sans-serif;--mat-menu-item-label-text-size: 16px;--mat-menu-item-label-text-tracking: 0.03125em;--mat-menu-item-label-text-line-height: 24px;--mat-menu-item-label-text-weight: 400}html{--mat-list-active-indicator-color: transparent;--mat-list-active-indicator-shape: 4px;--mat-list-list-item-container-shape: 0;--mat-list-list-item-leading-avatar-shape: 50%;--mat-list-list-item-container-color: transparent;--mat-list-list-item-selected-container-color: transparent;--mat-list-list-item-leading-avatar-color: transparent;--mat-list-list-item-leading-icon-size: 24px;--mat-list-list-item-leading-avatar-size: 40px;--mat-list-list-item-trailing-icon-size: 24px;--mat-list-list-item-disabled-state-layer-color: transparent;--mat-list-list-item-disabled-state-layer-opacity: 0;--mat-list-list-item-disabled-label-text-opacity: 0.38;--mat-list-list-item-disabled-leading-icon-opacity: 0.38;--mat-list-list-item-disabled-trailing-icon-opacity: 0.38}html{--mat-list-list-item-label-text-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-supporting-text-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-leading-icon-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-trailing-supporting-text-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-trailing-icon-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-selected-trailing-icon-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-disabled-label-text-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-disabled-leading-icon-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-disabled-trailing-icon-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-hover-label-text-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-hover-leading-icon-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-hover-state-layer-opacity: 0.04;--mat-list-list-item-hover-trailing-icon-color: rgba(0, 0, 0, 0.54);--mat-list-list-item-focus-label-text-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-list-list-item-focus-state-layer-opacity: 0.12}.mdc-list-item__start,.mdc-list-item__end{--mat-radio-checked-ripple-color: #673ab7;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #673ab7;--mat-radio-selected-hover-icon-color: #673ab7;--mat-radio-selected-icon-color: #673ab7;--mat-radio-selected-pressed-icon-color: #673ab7;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}.mat-accent .mdc-list-item__start,.mat-accent .mdc-list-item__end{--mat-radio-checked-ripple-color: #ffd740;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #ffd740;--mat-radio-selected-hover-icon-color: #ffd740;--mat-radio-selected-icon-color: #ffd740;--mat-radio-selected-pressed-icon-color: #ffd740;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}.mat-warn .mdc-list-item__start,.mat-warn .mdc-list-item__end{--mat-radio-checked-ripple-color: #f44336;--mat-radio-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-radio-disabled-selected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-disabled-unselected-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-label-text-color: rgba(0, 0, 0, 0.87);--mat-radio-ripple-color: rgba(0, 0, 0, 0.87);--mat-radio-selected-focus-icon-color: #f44336;--mat-radio-selected-hover-icon-color: #f44336;--mat-radio-selected-icon-color: #f44336;--mat-radio-selected-pressed-icon-color: #f44336;--mat-radio-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.87)}.mat-mdc-list-option{--mat-checkbox-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-label-text-color: rgba(0, 0, 0, 0.87);--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #673ab7;--mat-checkbox-selected-hover-icon-color: #673ab7;--mat-checkbox-selected-icon-color: #673ab7;--mat-checkbox-selected-pressed-icon-color: #673ab7;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #673ab7;--mat-checkbox-selected-hover-state-layer-color: #673ab7;--mat-checkbox-selected-pressed-state-layer-color: #673ab7;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-list-option.mat-accent{--mat-checkbox-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-label-text-color: rgba(0, 0, 0, 0.87);--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: rgba(0, 0, 0, 0.87);--mat-checkbox-selected-focus-icon-color: #ffd740;--mat-checkbox-selected-hover-icon-color: #ffd740;--mat-checkbox-selected-icon-color: #ffd740;--mat-checkbox-selected-pressed-icon-color: #ffd740;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #ffd740;--mat-checkbox-selected-hover-state-layer-color: #ffd740;--mat-checkbox-selected-pressed-state-layer-color: #ffd740;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-list-option.mat-warn{--mat-checkbox-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-label-text-color: rgba(0, 0, 0, 0.87);--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #f44336;--mat-checkbox-selected-hover-icon-color: #f44336;--mat-checkbox-selected-icon-color: #f44336;--mat-checkbox-selected-pressed-icon-color: #f44336;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #f44336;--mat-checkbox-selected-hover-state-layer-color: #f44336;--mat-checkbox-selected-pressed-state-layer-color: #f44336;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__start,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__start{color:#673ab7}.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__content,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__end{opacity:1}html{--mat-list-list-item-leading-icon-start-space: 16px;--mat-list-list-item-leading-icon-end-space: 32px;--mat-list-list-item-one-line-container-height: 48px;--mat-list-list-item-two-line-container-height: 64px;--mat-list-list-item-three-line-container-height: 88px}.mdc-list-item__start,.mdc-list-item__end{--mat-radio-state-layer-size: 40px;--mat-radio-touch-target-display: block}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line{height:56px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines{height:72px}html{--mat-list-list-item-label-text-font: Roboto, sans-serif;--mat-list-list-item-label-text-line-height: 24px;--mat-list-list-item-label-text-size: 16px;--mat-list-list-item-label-text-tracking: 0.03125em;--mat-list-list-item-label-text-weight: 400;--mat-list-list-item-supporting-text-font: Roboto, sans-serif;--mat-list-list-item-supporting-text-line-height: 20px;--mat-list-list-item-supporting-text-size: 14px;--mat-list-list-item-supporting-text-tracking: 0.0178571429em;--mat-list-list-item-supporting-text-weight: 400;--mat-list-list-item-trailing-supporting-text-font: Roboto, sans-serif;--mat-list-list-item-trailing-supporting-text-line-height: 20px;--mat-list-list-item-trailing-supporting-text-size: 12px;--mat-list-list-item-trailing-supporting-text-tracking: 0.0333333333em;--mat-list-list-item-trailing-supporting-text-weight: 400}.mdc-list-group__subheader{font:400 16px / 28px Roboto, sans-serif;letter-spacing:.009375em}html{--mat-paginator-container-text-color: rgba(0, 0, 0, 0.87);--mat-paginator-container-background-color: white;--mat-paginator-enabled-icon-color: rgba(0, 0, 0, 0.54);--mat-paginator-disabled-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}html{--mat-paginator-container-size: 56px;--mat-paginator-form-field-container-height: 40px;--mat-paginator-form-field-container-vertical-padding: 8px;--mat-paginator-touch-target-display: block}html{--mat-paginator-container-text-font: Roboto, sans-serif;--mat-paginator-container-text-line-height: 20px;--mat-paginator-container-text-size: 12px;--mat-paginator-container-text-tracking: 0.0333333333em;--mat-paginator-container-text-weight: 400;--mat-paginator-select-trigger-text-size: 12px}html{--mat-tab-container-height: 48px;--mat-tab-divider-color: transparent;--mat-tab-divider-height: 0;--mat-tab-active-indicator-height: 2px;--mat-tab-active-indicator-shape: 0}.mat-mdc-tab-group,.mat-mdc-tab-nav-bar{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, 0.54);--mat-tab-pagination-icon-color: rgba(0, 0, 0, 0.87);--mat-tab-inactive-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-label-text-color: #673ab7;--mat-tab-active-ripple-color: #673ab7;--mat-tab-inactive-ripple-color: #673ab7;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-focus-label-text-color: #673ab7;--mat-tab-active-hover-label-text-color: #673ab7;--mat-tab-active-focus-indicator-color: #673ab7;--mat-tab-active-hover-indicator-color: #673ab7;--mat-tab-active-indicator-color: #673ab7}.mat-mdc-tab-group.mat-accent,.mat-mdc-tab-nav-bar.mat-accent{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, 0.54);--mat-tab-pagination-icon-color: rgba(0, 0, 0, 0.87);--mat-tab-inactive-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-label-text-color: #ffd740;--mat-tab-active-ripple-color: #ffd740;--mat-tab-inactive-ripple-color: #ffd740;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-focus-label-text-color: #ffd740;--mat-tab-active-hover-label-text-color: #ffd740;--mat-tab-active-focus-indicator-color: #ffd740;--mat-tab-active-hover-indicator-color: #ffd740;--mat-tab-active-indicator-color: #ffd740}.mat-mdc-tab-group.mat-warn,.mat-mdc-tab-nav-bar.mat-warn{--mat-tab-disabled-ripple-color: rgba(0, 0, 0, 0.54);--mat-tab-pagination-icon-color: rgba(0, 0, 0, 0.87);--mat-tab-inactive-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-label-text-color: #f44336;--mat-tab-active-ripple-color: #f44336;--mat-tab-inactive-ripple-color: #f44336;--mat-tab-inactive-focus-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-inactive-hover-label-text-color: rgba(0, 0, 0, 0.54);--mat-tab-active-focus-label-text-color: #f44336;--mat-tab-active-hover-label-text-color: #f44336;--mat-tab-active-focus-indicator-color: #f44336;--mat-tab-active-hover-indicator-color: #f44336;--mat-tab-active-indicator-color: #f44336}.mat-mdc-tab-group.mat-background-primary,.mat-mdc-tab-nav-bar.mat-background-primary{--mat-tab-background-color: #673ab7;--mat-tab-foreground-color: white}.mat-mdc-tab-group.mat-background-accent,.mat-mdc-tab-nav-bar.mat-background-accent{--mat-tab-background-color: #ffd740;--mat-tab-foreground-color: rgba(0, 0, 0, 0.87)}.mat-mdc-tab-group.mat-background-warn,.mat-mdc-tab-nav-bar.mat-background-warn{--mat-tab-background-color: #f44336;--mat-tab-foreground-color: white}.mat-mdc-tab-header{--mat-tab-container-height: 48px}.mat-mdc-tab-header{--mat-tab-label-text-font: Roboto, sans-serif;--mat-tab-label-text-size: 14px;--mat-tab-label-text-tracking: 0.0892857143em;--mat-tab-label-text-line-height: 36px;--mat-tab-label-text-weight: 500}html{--mat-checkbox-disabled-selected-checkmark-color: white;--mat-checkbox-selected-focus-state-layer-opacity: 0.12;--mat-checkbox-selected-hover-state-layer-opacity: 0.04;--mat-checkbox-selected-pressed-state-layer-opacity: 0.12;--mat-checkbox-unselected-focus-state-layer-opacity: 0.12;--mat-checkbox-unselected-hover-state-layer-opacity: 0.04;--mat-checkbox-unselected-pressed-state-layer-opacity: 0.12}html{--mat-checkbox-disabled-label-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-label-text-color: rgba(0, 0, 0, 0.87);--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: rgba(0, 0, 0, 0.87);--mat-checkbox-selected-focus-icon-color: #ffd740;--mat-checkbox-selected-hover-icon-color: #ffd740;--mat-checkbox-selected-icon-color: #ffd740;--mat-checkbox-selected-pressed-icon-color: #ffd740;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #ffd740;--mat-checkbox-selected-hover-state-layer-color: #ffd740;--mat-checkbox-selected-pressed-state-layer-color: #ffd740;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-checkbox.mat-primary{--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #673ab7;--mat-checkbox-selected-hover-icon-color: #673ab7;--mat-checkbox-selected-icon-color: #673ab7;--mat-checkbox-selected-pressed-icon-color: #673ab7;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #673ab7;--mat-checkbox-selected-hover-state-layer-color: #673ab7;--mat-checkbox-selected-pressed-state-layer-color: #673ab7;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-checkbox.mat-warn{--mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-checkbox-selected-checkmark-color: white;--mat-checkbox-selected-focus-icon-color: #f44336;--mat-checkbox-selected-hover-icon-color: #f44336;--mat-checkbox-selected-icon-color: #f44336;--mat-checkbox-selected-pressed-icon-color: #f44336;--mat-checkbox-unselected-focus-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-icon-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54);--mat-checkbox-selected-focus-state-layer-color: #f44336;--mat-checkbox-selected-hover-state-layer-color: #f44336;--mat-checkbox-selected-pressed-state-layer-color: #f44336;--mat-checkbox-unselected-focus-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-hover-state-layer-color: rgba(0, 0, 0, 0.87);--mat-checkbox-unselected-pressed-state-layer-color: rgba(0, 0, 0, 0.87)}html{--mat-checkbox-touch-target-display: block;--mat-checkbox-state-layer-size: 40px}html{--mat-checkbox-label-text-font: Roboto, sans-serif;--mat-checkbox-label-text-line-height: 20px;--mat-checkbox-label-text-size: 14px;--mat-checkbox-label-text-tracking: 0.0178571429em;--mat-checkbox-label-text-weight: 400}html{--mat-button-filled-container-shape: 4px;--mat-button-filled-horizontal-padding: 16px;--mat-button-filled-icon-offset: -4px;--mat-button-filled-icon-spacing: 8px;--mat-button-outlined-container-shape: 4px;--mat-button-outlined-horizontal-padding: 15px;--mat-button-outlined-icon-offset: -4px;--mat-button-outlined-icon-spacing: 8px;--mat-button-outlined-keep-touch-target: false;--mat-button-outlined-outline-width: 1px;--mat-button-protected-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mat-button-protected-container-shape: 4px;--mat-button-protected-disabled-container-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-button-protected-focus-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);--mat-button-protected-horizontal-padding: 16px;--mat-button-protected-hover-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);--mat-button-protected-icon-offset: -4px;--mat-button-protected-icon-spacing: 8px;--mat-button-protected-pressed-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-button-text-container-shape: 4px;--mat-button-text-horizontal-padding: 8px;--mat-button-text-icon-offset: 0;--mat-button-text-icon-spacing: 8px;--mat-button-text-with-icon-horizontal-padding: 8px;--mat-button-tonal-container-shape: 4px;--mat-button-tonal-horizontal-padding: 16px;--mat-button-tonal-icon-offset: -4px;--mat-button-tonal-icon-spacing: 8px}html{--mat-button-filled-container-color: white;--mat-button-filled-disabled-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-filled-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-filled-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-button-filled-focus-state-layer-opacity: 0.12;--mat-button-filled-hover-state-layer-opacity: 0.04;--mat-button-filled-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-filled-pressed-state-layer-opacity: 0.12;--mat-button-filled-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-filled-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-outlined-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-outlined-disabled-outline-color: rgba(0, 0, 0, 0.12);--mat-button-outlined-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-button-outlined-focus-state-layer-opacity: 0.12;--mat-button-outlined-hover-state-layer-opacity: 0.04;--mat-button-outlined-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-outlined-outline-color: rgba(0, 0, 0, 0.12);--mat-button-outlined-pressed-state-layer-opacity: 0.12;--mat-button-outlined-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-outlined-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-protected-container-color: white;--mat-button-protected-disabled-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-protected-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-protected-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-button-protected-focus-state-layer-opacity: 0.12;--mat-button-protected-hover-state-layer-opacity: 0.04;--mat-button-protected-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-protected-pressed-state-layer-opacity: 0.12;--mat-button-protected-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-protected-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-text-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-text-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-button-text-focus-state-layer-opacity: 0.12;--mat-button-text-hover-state-layer-opacity: 0.04;--mat-button-text-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-text-pressed-state-layer-opacity: 0.12;--mat-button-text-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-text-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-tonal-container-color: white;--mat-button-tonal-disabled-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-tonal-disabled-label-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-tonal-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-button-tonal-focus-state-layer-opacity: 0.12;--mat-button-tonal-hover-state-layer-opacity: 0.04;--mat-button-tonal-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-tonal-pressed-state-layer-opacity: 0.12;--mat-button-tonal-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-tonal-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-button.mat-primary,.mat-mdc-unelevated-button.mat-primary,.mat-mdc-raised-button.mat-primary,.mat-mdc-outlined-button.mat-primary,.mat-tonal-button.mat-primary{--mat-button-filled-container-color: #673ab7;--mat-button-filled-label-text-color: white;--mat-button-filled-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-filled-state-layer-color: white;--mat-button-outlined-label-text-color: #673ab7;--mat-button-outlined-outline-color: rgba(0, 0, 0, 0.12);--mat-button-outlined-ripple-color: color-mix(in srgb, #673ab7 12%, transparent);--mat-button-outlined-state-layer-color: #673ab7;--mat-button-protected-container-color: #673ab7;--mat-button-protected-label-text-color: white;--mat-button-protected-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-protected-state-layer-color: white;--mat-button-text-label-text-color: #673ab7;--mat-button-text-ripple-color: color-mix(in srgb, #673ab7 12%, transparent);--mat-button-text-state-layer-color: #673ab7;--mat-button-tonal-container-color: #673ab7;--mat-button-tonal-label-text-color: white;--mat-button-tonal-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-tonal-state-layer-color: white}.mat-mdc-button.mat-accent,.mat-mdc-unelevated-button.mat-accent,.mat-mdc-raised-button.mat-accent,.mat-mdc-outlined-button.mat-accent,.mat-tonal-button.mat-accent{--mat-button-filled-container-color: #ffd740;--mat-button-filled-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-filled-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-filled-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-outlined-label-text-color: #ffd740;--mat-button-outlined-outline-color: rgba(0, 0, 0, 0.12);--mat-button-outlined-ripple-color: color-mix(in srgb, #ffd740 12%, transparent);--mat-button-outlined-state-layer-color: #ffd740;--mat-button-protected-container-color: #ffd740;--mat-button-protected-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-protected-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-protected-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-text-label-text-color: #ffd740;--mat-button-text-ripple-color: color-mix(in srgb, #ffd740 12%, transparent);--mat-button-text-state-layer-color: #ffd740;--mat-button-tonal-container-color: #ffd740;--mat-button-tonal-label-text-color: rgba(0, 0, 0, 0.87);--mat-button-tonal-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-tonal-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-button.mat-warn,.mat-mdc-unelevated-button.mat-warn,.mat-mdc-raised-button.mat-warn,.mat-mdc-outlined-button.mat-warn,.mat-tonal-button.mat-warn{--mat-button-filled-container-color: #f44336;--mat-button-filled-label-text-color: white;--mat-button-filled-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-filled-state-layer-color: white;--mat-button-outlined-label-text-color: #f44336;--mat-button-outlined-outline-color: rgba(0, 0, 0, 0.12);--mat-button-outlined-ripple-color: color-mix(in srgb, #f44336 12%, transparent);--mat-button-outlined-state-layer-color: #f44336;--mat-button-protected-container-color: #f44336;--mat-button-protected-label-text-color: white;--mat-button-protected-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-protected-state-layer-color: white;--mat-button-text-label-text-color: #f44336;--mat-button-text-ripple-color: color-mix(in srgb, #f44336 12%, transparent);--mat-button-text-state-layer-color: #f44336;--mat-button-tonal-container-color: #f44336;--mat-button-tonal-label-text-color: white;--mat-button-tonal-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-button-tonal-state-layer-color: white}html{--mat-button-filled-container-height: 36px;--mat-button-filled-touch-target-display: block;--mat-button-outlined-container-height: 36px;--mat-button-outlined-touch-target-display: block;--mat-button-protected-container-height: 36px;--mat-button-protected-touch-target-display: block;--mat-button-text-container-height: 36px;--mat-button-text-touch-target-display: block;--mat-button-tonal-container-height: 36px;--mat-button-tonal-touch-target-display: block}html{--mat-button-filled-label-text-font: Roboto, sans-serif;--mat-button-filled-label-text-size: 14px;--mat-button-filled-label-text-tracking: 0.0892857143em;--mat-button-filled-label-text-transform: none;--mat-button-filled-label-text-weight: 500;--mat-button-outlined-label-text-font: Roboto, sans-serif;--mat-button-outlined-label-text-size: 14px;--mat-button-outlined-label-text-tracking: 0.0892857143em;--mat-button-outlined-label-text-transform: none;--mat-button-outlined-label-text-weight: 500;--mat-button-protected-label-text-font: Roboto, sans-serif;--mat-button-protected-label-text-size: 14px;--mat-button-protected-label-text-tracking: 0.0892857143em;--mat-button-protected-label-text-transform: none;--mat-button-protected-label-text-weight: 500;--mat-button-text-label-text-font: Roboto, sans-serif;--mat-button-text-label-text-size: 14px;--mat-button-text-label-text-tracking: 0.0892857143em;--mat-button-text-label-text-transform: none;--mat-button-text-label-text-weight: 500;--mat-button-tonal-label-text-font: Roboto, sans-serif;--mat-button-tonal-label-text-size: 14px;--mat-button-tonal-label-text-tracking: 0.0892857143em;--mat-button-tonal-label-text-transform: none;--mat-button-tonal-label-text-weight: 500}html{--mat-icon-button-icon-size: 24px;--mat-icon-button-container-shape: 50%}html{--mat-icon-button-disabled-icon-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-icon-button-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-icon-button-focus-state-layer-opacity: 0.12;--mat-icon-button-hover-state-layer-opacity: 0.04;--mat-icon-button-icon-color: inherit;--mat-icon-button-pressed-state-layer-opacity: 0.12;--mat-icon-button-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-icon-button-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-icon-button.mat-primary{--mat-icon-button-icon-color: #673ab7;--mat-icon-button-state-layer-color: #673ab7;--mat-icon-button-ripple-color: color-mix(in srgb, #673ab7 12%, transparent)}.mat-mdc-icon-button.mat-accent{--mat-icon-button-icon-color: #ffd740;--mat-icon-button-state-layer-color: #ffd740;--mat-icon-button-ripple-color: color-mix(in srgb, #ffd740 12%, transparent)}.mat-mdc-icon-button.mat-warn{--mat-icon-button-icon-color: #f44336;--mat-icon-button-state-layer-color: #f44336;--mat-icon-button-ripple-color: color-mix(in srgb, #f44336 12%, transparent)}html{--mat-icon-button-touch-target-display: block}.mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size: 48px;--mat-icon-button-state-layer-size: 48px;width:var(--mat-icon-button-state-layer-size);height:var(--mat-icon-button-state-layer-size);padding:12px}html{--mat-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mat-fab-container-shape: 50%;--mat-fab-extended-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mat-fab-extended-container-height: 48px;--mat-fab-extended-container-shape: 24px;--mat-fab-extended-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-extended-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-extended-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);--mat-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);--mat-fab-small-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mat-fab-small-container-shape: 50%;--mat-fab-small-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-small-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-fab-small-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}html{--mat-fab-container-color: white;--mat-fab-disabled-state-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-fab-disabled-state-foreground-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-fab-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-fab-focus-state-layer-opacity: 0.12;--mat-fab-foreground-color: rgba(0, 0, 0, 0.87);--mat-fab-hover-state-layer-opacity: 0.04;--mat-fab-pressed-state-layer-opacity: 0.12;--mat-fab-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-fab-small-container-color: white;--mat-fab-small-disabled-state-container-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-fab-small-disabled-state-foreground-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-fab-small-disabled-state-layer-color: rgba(0, 0, 0, 0.54);--mat-fab-small-focus-state-layer-opacity: 0.12;--mat-fab-small-foreground-color: rgba(0, 0, 0, 0.87);--mat-fab-small-hover-state-layer-opacity: 0.04;--mat-fab-small-pressed-state-layer-opacity: 0.12;--mat-fab-small-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-fab-small-state-layer-color: rgba(0, 0, 0, 0.87);--mat-fab-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-fab.mat-primary,.mat-mdc-mini-fab.mat-primary{--mat-fab-container-color: #673ab7;--mat-fab-foreground-color: white;--mat-fab-ripple-color: color-mix(in srgb, #673ab7 12%, transparent);--mat-fab-small-container-color: #673ab7;--mat-fab-small-foreground-color: white;--mat-fab-small-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-fab-small-state-layer-color: white;--mat-fab-state-layer-color: white}.mat-mdc-fab.mat-accent,.mat-mdc-mini-fab.mat-accent{--mat-fab-container-color: #ffd740;--mat-fab-foreground-color: rgba(0, 0, 0, 0.87);--mat-fab-ripple-color: color-mix(in srgb, #ffd740 12%, transparent);--mat-fab-small-container-color: #ffd740;--mat-fab-small-foreground-color: rgba(0, 0, 0, 0.87);--mat-fab-small-ripple-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-fab-small-state-layer-color: rgba(0, 0, 0, 0.87);--mat-fab-state-layer-color: rgba(0, 0, 0, 0.87)}.mat-mdc-fab.mat-warn,.mat-mdc-mini-fab.mat-warn{--mat-fab-container-color: #f44336;--mat-fab-foreground-color: white;--mat-fab-ripple-color: color-mix(in srgb, #f44336 12%, transparent);--mat-fab-small-container-color: #f44336;--mat-fab-small-foreground-color: white;--mat-fab-small-ripple-color: color-mix(in srgb, white 12%, transparent);--mat-fab-small-state-layer-color: white;--mat-fab-state-layer-color: white}html{--mat-fab-small-touch-target-display: block;--mat-fab-touch-target-display: block}html{--mat-fab-extended-label-text-font: Roboto, sans-serif;--mat-fab-extended-label-text-size: 14px;--mat-fab-extended-label-text-tracking: 0.0892857143em;--mat-fab-extended-label-text-weight: 500}html{--mat-snack-bar-container-shape: 4px}html{--mat-snack-bar-container-color: #424242;--mat-snack-bar-supporting-text-color: white;--mat-snack-bar-button-color: #9575cd}html{--mat-snack-bar-supporting-text-font: Roboto, sans-serif;--mat-snack-bar-supporting-text-line-height: 20px;--mat-snack-bar-supporting-text-size: 14px;--mat-snack-bar-supporting-text-weight: 400}html{--mat-table-row-item-outline-width: 1px}html{--mat-table-background-color: white;--mat-table-header-headline-color: rgba(0, 0, 0, 0.87);--mat-table-row-item-label-text-color: rgba(0, 0, 0, 0.87);--mat-table-row-item-outline-color: rgba(0, 0, 0, 0.12)}html{--mat-table-header-container-height: 56px;--mat-table-footer-container-height: 52px;--mat-table-row-item-container-height: 52px}html{--mat-table-header-headline-font: Roboto, sans-serif;--mat-table-header-headline-line-height: 22px;--mat-table-header-headline-size: 14px;--mat-table-header-headline-weight: 500;--mat-table-header-headline-tracking: 0.0071428571em;--mat-table-row-item-label-text-font: Roboto, sans-serif;--mat-table-row-item-label-text-line-height: 20px;--mat-table-row-item-label-text-size: 14px;--mat-table-row-item-label-text-weight: 400;--mat-table-row-item-label-text-tracking: 0.0178571429em;--mat-table-footer-supporting-text-font: Roboto, sans-serif;--mat-table-footer-supporting-text-line-height: 20px;--mat-table-footer-supporting-text-size: 14px;--mat-table-footer-supporting-text-weight: 400;--mat-table-footer-supporting-text-tracking: 0.0178571429em}html{--mat-progress-spinner-active-indicator-width: 4px;--mat-progress-spinner-size: 48px}html{--mat-progress-spinner-active-indicator-color: #673ab7}.mat-accent{--mat-progress-spinner-active-indicator-color: #ffd740}.mat-warn{--mat-progress-spinner-active-indicator-color: #f44336}html{--mat-badge-container-shape: 50%;--mat-badge-container-size: unset;--mat-badge-small-size-container-size: unset;--mat-badge-large-size-container-size: unset;--mat-badge-legacy-container-size: 22px;--mat-badge-legacy-small-size-container-size: 16px;--mat-badge-legacy-large-size-container-size: 28px;--mat-badge-container-offset: -11px 0;--mat-badge-small-size-container-offset: -8px 0;--mat-badge-large-size-container-offset: -14px 0;--mat-badge-container-overlap-offset: -11px;--mat-badge-small-size-container-overlap-offset: -8px;--mat-badge-large-size-container-overlap-offset: -14px;--mat-badge-container-padding: 0;--mat-badge-small-size-container-padding: 0;--mat-badge-large-size-container-padding: 0}html{--mat-badge-background-color: #673ab7;--mat-badge-text-color: white;--mat-badge-disabled-state-background-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-badge-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent)}.mat-badge-accent{--mat-badge-background-color: #ffd740;--mat-badge-text-color: rgba(0, 0, 0, 0.87)}.mat-badge-warn{--mat-badge-background-color: #f44336;--mat-badge-text-color: white}html{--mat-badge-text-font: Roboto, sans-serif;--mat-badge-line-height: 22px;--mat-badge-text-size: 12px;--mat-badge-text-weight: 600;--mat-badge-small-size-text-size: 9px;--mat-badge-small-size-line-height: 16px;--mat-badge-large-size-text-size: 24px;--mat-badge-large-size-line-height: 28px}html{--mat-bottom-sheet-container-shape: 4px}html{--mat-bottom-sheet-container-text-color: rgba(0, 0, 0, 0.87);--mat-bottom-sheet-container-background-color: white}html{--mat-bottom-sheet-container-text-font: Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height: 20px;--mat-bottom-sheet-container-text-size: 14px;--mat-bottom-sheet-container-text-tracking: 0.0178571429em;--mat-bottom-sheet-container-text-weight: 400}html{--mat-button-toggle-focus-state-layer-opacity: 0.12;--mat-button-toggle-hover-state-layer-opacity: 0.04;--mat-button-toggle-legacy-focus-state-layer-opacity: 1;--mat-button-toggle-legacy-height: 36px;--mat-button-toggle-legacy-shape: 2px;--mat-button-toggle-shape: 4px}html{--mat-button-toggle-background-color: white;--mat-button-toggle-disabled-selected-state-background-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-toggle-disabled-selected-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-toggle-disabled-state-background-color: white;--mat-button-toggle-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-toggle-divider-color: rgba(0, 0, 0, 0.12);--mat-button-toggle-legacy-disabled-selected-state-background-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-toggle-legacy-disabled-state-background-color: white;--mat-button-toggle-legacy-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-button-toggle-legacy-selected-state-background-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-toggle-legacy-selected-state-text-color: rgba(0, 0, 0, 0.87);--mat-button-toggle-legacy-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-toggle-legacy-text-color: rgba(0, 0, 0, 0.87);--mat-button-toggle-selected-state-background-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-button-toggle-selected-state-text-color: rgba(0, 0, 0, 0.87);--mat-button-toggle-state-layer-color: rgba(0, 0, 0, 0.87);--mat-button-toggle-text-color: rgba(0, 0, 0, 0.87)}html{--mat-button-toggle-height: 48px}html{--mat-button-toggle-label-text-font: Roboto, sans-serif;--mat-button-toggle-label-text-line-height: 24px;--mat-button-toggle-label-text-size: 16px;--mat-button-toggle-label-text-tracking: 0.03125em;--mat-button-toggle-label-text-weight: 400;--mat-button-toggle-legacy-label-text-font: Roboto, sans-serif;--mat-button-toggle-legacy-label-text-line-height: 24px;--mat-button-toggle-legacy-label-text-size: 16px;--mat-button-toggle-legacy-label-text-tracking: 0.03125em;--mat-button-toggle-legacy-label-text-weight: 400}html{--mat-datepicker-calendar-container-shape: 4px;--mat-datepicker-calendar-container-touch-shape: 4px;--mat-datepicker-calendar-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-container-touch-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)}html{--mat-datepicker-calendar-date-in-range-state-background-color: color-mix(in srgb, #673ab7 20%, transparent);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: color-mix(in srgb, #ffd740 20%, transparent);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #673ab7;--mat-datepicker-calendar-date-selected-disabled-state-background-color: color-mix(in srgb, #673ab7 38%, transparent);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: color-mix(in srgb, #673ab7 12%, transparent);--mat-datepicker-calendar-date-hover-state-background-color: color-mix(in srgb, #673ab7 4%, transparent);--mat-datepicker-toggle-active-state-icon-color: #673ab7;--mat-datepicker-toggle-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-period-button-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-navigation-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-disabled-state-outline-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-outline-color: transparent;--mat-datepicker-calendar-date-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-preview-state-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-range-input-separator-color: rgba(0, 0, 0, 0.87);--mat-datepicker-range-input-disabled-state-separator-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-range-input-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-container-background-color: white;--mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, 0.87)}.mat-datepicker-content.mat-accent,.mat-datepicker-toggle-active.mat-accent{--mat-datepicker-calendar-date-in-range-state-background-color: color-mix(in srgb, #ffd740 20%, transparent);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: color-mix(in srgb, #ffd740 20%, transparent);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-calendar-date-selected-state-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-selected-state-background-color: #ffd740;--mat-datepicker-calendar-date-selected-disabled-state-background-color: color-mix(in srgb, #ffd740 38%, transparent);--mat-datepicker-calendar-date-today-selected-state-outline-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-focus-state-background-color: color-mix(in srgb, #ffd740 12%, transparent);--mat-datepicker-calendar-date-hover-state-background-color: color-mix(in srgb, #ffd740 4%, transparent);--mat-datepicker-toggle-active-state-icon-color: #ffd740;--mat-datepicker-toggle-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-period-button-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-navigation-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-disabled-state-outline-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-outline-color: transparent;--mat-datepicker-calendar-date-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-preview-state-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-range-input-separator-color: rgba(0, 0, 0, 0.87);--mat-datepicker-range-input-disabled-state-separator-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-range-input-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-container-background-color: white;--mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, 0.87)}.mat-datepicker-content.mat-warn,.mat-datepicker-toggle-active.mat-warn{--mat-datepicker-calendar-date-in-range-state-background-color: color-mix(in srgb, #f44336 20%, transparent);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: color-mix(in srgb, #ffd740 20%, transparent);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #f44336;--mat-datepicker-calendar-date-selected-disabled-state-background-color: color-mix(in srgb, #f44336 38%, transparent);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: color-mix(in srgb, #f44336 12%, transparent);--mat-datepicker-calendar-date-hover-state-background-color: color-mix(in srgb, #f44336 4%, transparent);--mat-datepicker-toggle-active-state-icon-color: #f44336;--mat-datepicker-toggle-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-period-button-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-navigation-button-icon-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-disabled-state-outline-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-outline-color: transparent;--mat-datepicker-calendar-date-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-date-preview-state-outline-color: rgba(0, 0, 0, 0.54);--mat-datepicker-range-input-separator-color: rgba(0, 0, 0, 0.87);--mat-datepicker-range-input-disabled-state-separator-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-range-input-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-datepicker-calendar-container-background-color: white;--mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, 0.87)}.mat-calendar-controls{--mat-icon-button-touch-target-display: none}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size: 40px;--mat-icon-button-state-layer-size: 40px;width:var(--mat-icon-button-state-layer-size);height:var(--mat-icon-button-state-layer-size);padding:8px}html{--mat-datepicker-calendar-text-font: Roboto, sans-serif;--mat-datepicker-calendar-text-size: 13px;--mat-datepicker-calendar-body-label-text-size: 14px;--mat-datepicker-calendar-body-label-text-weight: 500;--mat-datepicker-calendar-period-button-text-size: 14px;--mat-datepicker-calendar-period-button-text-weight: 500;--mat-datepicker-calendar-header-text-size: 11px;--mat-datepicker-calendar-header-text-weight: 400}html{--mat-divider-width: 1px}html{--mat-divider-color: rgba(0, 0, 0, 0.12)}html{--mat-expansion-container-shape: 4px;--mat-expansion-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mat-expansion-legacy-header-indicator-display: inline-block;--mat-expansion-header-indicator-display: none}html{--mat-expansion-container-background-color: white;--mat-expansion-container-text-color: rgba(0, 0, 0, 0.87);--mat-expansion-actions-divider-color: rgba(0, 0, 0, 0.12);--mat-expansion-header-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-expansion-header-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-expansion-header-disabled-state-text-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 38%, transparent);--mat-expansion-header-text-color: rgba(0, 0, 0, 0.87);--mat-expansion-header-description-color: rgba(0, 0, 0, 0.54);--mat-expansion-header-indicator-color: rgba(0, 0, 0, 0.54)}html{--mat-expansion-header-collapsed-state-height: 48px;--mat-expansion-header-expanded-state-height: 64px}html{--mat-expansion-header-text-font: Roboto, sans-serif;--mat-expansion-header-text-size: 14px;--mat-expansion-header-text-weight: 500;--mat-expansion-header-text-line-height: inherit;--mat-expansion-header-text-tracking: inherit;--mat-expansion-container-text-font: Roboto, sans-serif;--mat-expansion-container-text-line-height: 20px;--mat-expansion-container-text-size: 14px;--mat-expansion-container-text-tracking: 0.0178571429em;--mat-expansion-container-text-weight: 400}html{--mat-grid-list-tile-header-primary-text-size: 14px;--mat-grid-list-tile-header-secondary-text-size: 12px;--mat-grid-list-tile-footer-primary-text-size: 14px;--mat-grid-list-tile-footer-secondary-text-size: 12px}html{--mat-icon-color: inherit}.mat-icon.mat-primary{--mat-icon-color: #673ab7}.mat-icon.mat-accent{--mat-icon-color: #ffd740}.mat-icon.mat-warn{--mat-icon-color: #f44336}html{--mat-sidenav-container-shape: 0;--mat-sidenav-container-elevation-shadow: 0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);--mat-sidenav-container-width: auto}html{--mat-sidenav-container-divider-color: rgba(0, 0, 0, 0.12);--mat-sidenav-container-background-color: white;--mat-sidenav-container-text-color: rgba(0, 0, 0, 0.87);--mat-sidenav-content-background-color: #fafafa;--mat-sidenav-content-text-color: rgba(0, 0, 0, 0.87);--mat-sidenav-scrim-color: rgba(0, 0, 0, 0.6)}html{--mat-stepper-header-focus-state-layer-shape: 0;--mat-stepper-header-hover-state-layer-shape: 0}html{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #673ab7;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #673ab7;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #673ab7;--mat-stepper-header-edit-state-icon-foreground-color: white;--mat-stepper-container-color: white;--mat-stepper-line-color: rgba(0, 0, 0, 0.12);--mat-stepper-header-hover-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 4%, transparent);--mat-stepper-header-focus-state-layer-color: color-mix(in srgb, rgba(0, 0, 0, 0.87) 12%, transparent);--mat-stepper-header-label-text-color: rgba(0, 0, 0, 0.54);--mat-stepper-header-optional-label-text-color: rgba(0, 0, 0, 0.54);--mat-stepper-header-selected-state-label-text-color: rgba(0, 0, 0, 0.87);--mat-stepper-header-error-state-label-text-color: #f44336;--mat-stepper-header-icon-background-color: rgba(0, 0, 0, 0.54);--mat-stepper-header-error-state-icon-foreground-color: #f44336;--mat-stepper-header-error-state-icon-background-color: transparent}.mat-step-header.mat-accent{--mat-stepper-header-icon-foreground-color: rgba(0, 0, 0, 0.87);--mat-stepper-header-selected-state-icon-background-color: #ffd740;--mat-stepper-header-selected-state-icon-foreground-color: rgba(0, 0, 0, 0.87);--mat-stepper-header-done-state-icon-background-color: #ffd740;--mat-stepper-header-done-state-icon-foreground-color: rgba(0, 0, 0, 0.87);--mat-stepper-header-edit-state-icon-background-color: #ffd740;--mat-stepper-header-edit-state-icon-foreground-color: rgba(0, 0, 0, 0.87)}.mat-step-header.mat-warn{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #f44336;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #f44336;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #f44336;--mat-stepper-header-edit-state-icon-foreground-color: white}html{--mat-stepper-header-height: 72px}html{--mat-stepper-container-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-size: 14px;--mat-stepper-header-label-text-weight: 400;--mat-stepper-header-error-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-weight: 400}html{--mat-sort-arrow-color: rgba(0, 0, 0, 0.87)}html{--mat-toolbar-container-background-color: white;--mat-toolbar-container-text-color: rgba(0, 0, 0, 0.87)}.mat-toolbar.mat-primary{--mat-toolbar-container-background-color: #673ab7;--mat-toolbar-container-text-color: white}.mat-toolbar.mat-accent{--mat-toolbar-container-background-color: #ffd740;--mat-toolbar-container-text-color: rgba(0, 0, 0, 0.87)}.mat-toolbar.mat-warn{--mat-toolbar-container-background-color: #f44336;--mat-toolbar-container-text-color: white}html{--mat-toolbar-standard-height: 64px;--mat-toolbar-mobile-height: 56px}html{--mat-toolbar-title-text-font: Roboto, sans-serif;--mat-toolbar-title-text-line-height: 32px;--mat-toolbar-title-text-size: 20px;--mat-toolbar-title-text-tracking: 0.0125em;--mat-toolbar-title-text-weight: 500}html{--mat-tree-container-background-color: white;--mat-tree-node-text-color: rgba(0, 0, 0, 0.87)}html{--mat-tree-node-min-height: 48px}html{--mat-tree-node-text-font: Roboto, sans-serif;--mat-tree-node-text-size: 14px;--mat-tree-node-text-weight: 400}html{--mat-timepicker-container-shape: 4px;--mat-timepicker-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-timepicker-container-background-color: white}.mat-h1,.mat-headline-5,.mat-typography .mat-h1,.mat-typography .mat-headline-5,.mat-typography h1{font:400 24px/32px Roboto, sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2,.mat-headline-6,.mat-typography .mat-h2,.mat-typography .mat-headline-6,.mat-typography h2{font:500 20px/32px Roboto, sans-serif;letter-spacing:.0125em;margin:0 0 16px}.mat-h3,.mat-subtitle-1,.mat-typography .mat-h3,.mat-typography .mat-subtitle-1,.mat-typography h3{font:400 16px/28px Roboto, sans-serif;letter-spacing:.009375em;margin:0 0 16px}.mat-h4,.mat-body-1,.mat-typography .mat-h4,.mat-typography .mat-body-1,.mat-typography h4{font:400 16px/24px Roboto, sans-serif;letter-spacing:.03125em;margin:0 0 16px}.mat-h5,.mat-typography .mat-h5,.mat-typography h5{font:400 calc(14px*.83)/20px Roboto, sans-serif;margin:0 0 12px}.mat-h6,.mat-typography .mat-h6,.mat-typography h6{font:400 calc(14px*.67)/20px Roboto, sans-serif;margin:0 0 12px}.mat-body-strong,.mat-subtitle-2,.mat-typography .mat-body-strong,.mat-typography .mat-subtitle-2{font:500 14px/22px Roboto, sans-serif;letter-spacing:.0071428571em}.mat-body,.mat-body-2,.mat-typography .mat-body,.mat-typography .mat-body-2,.mat-typography{font:400 14px/20px Roboto, sans-serif;letter-spacing:.0178571429em}.mat-body p,.mat-body-2 p,.mat-typography .mat-body p,.mat-typography .mat-body-2 p,.mat-typography p{margin:0 0 12px}.mat-small,.mat-caption,.mat-typography .mat-small,.mat-typography .mat-caption{font:400 12px/20px Roboto, sans-serif;letter-spacing:.0333333333em}.mat-headline-1,.mat-typography .mat-headline-1{font:300 96px/96px Roboto, sans-serif;letter-spacing:-0.015625em;margin:0 0 56px}.mat-headline-2,.mat-typography .mat-headline-2{font:300 60px/60px Roboto, sans-serif;letter-spacing:-.0083333333em;margin:0 0 64px}.mat-headline-3,.mat-typography .mat-headline-3{font:400 48px/50px Roboto, sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-headline-4,.mat-typography .mat-headline-4{font:400 34px/40px Roboto, sans-serif;letter-spacing:.0073529412em;margin:0 0 64px}
