{"$schema": "http://json-schema.org/draft-07/schema", "$id": "angular-material-ng-add", "title": "Angular Material ng-add schematic", "type": "object", "properties": {"project": {"type": "string", "description": "Name of the project.", "$default": {"$source": "projectName"}}, "theme": {"description": "The theme to apply", "type": "string", "default": "azure-blue", "x-prompt": {"message": "Select a pair of starter prebuilt color palettes for your Angular Material theme", "type": "list", "items": [{"value": "azure-blue", "label": "Azure/Blue         [Preview: https://material.angular.dev?theme=azure-blue]"}, {"value": "rose-red", "label": "Rose/Red           [Preview: https://material.angular.dev?theme=rose-red]"}, {"value": "magenta-violet", "label": "Magenta/Violet     [Preview: https://material.angular.dev?theme=magenta-violet]"}, {"value": "cyan-orange", "label": "Cyan/Orange        [Preview: https://material.angular.dev?theme=cyan-orange]"}]}}}, "required": []}