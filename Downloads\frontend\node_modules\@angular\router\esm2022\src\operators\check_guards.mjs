/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { runInInjectionContext } from '@angular/core';
import { concat, defer, from, of, pipe, } from 'rxjs';
import { concatMap, first, map, mergeMap, tap } from 'rxjs/operators';
import { ActivationStart, ChildActivationStart } from '../events';
import { redirectingNavigationError } from '../navigation_canceling_error';
import { wrapIntoObservable } from '../utils/collection';
import { getClosestRouteInjector } from '../utils/config';
import { getCanActivateChild, getTokenOrFunctionIdentity, } from '../utils/preactivation';
import { isBoolean, isCanActivate, isCanActivateChild, isCanDeactivate, isCanLoad, isCanMatch, } from '../utils/type_guards';
import { prioritizedGuardValue } from './prioritized_guard_value';
export function checkGuards(injector, forwardEvent) {
    return mergeMap((t) => {
        const { targetSnapshot, currentSnapshot, guards: { canActivateChecks, canDeactivateChecks }, } = t;
        if (canDeactivateChecks.length === 0 && canActivateChecks.length === 0) {
            return of({ ...t, guardsResult: true });
        }
        return runCanDeactivateChecks(canDeactivateChecks, targetSnapshot, currentSnapshot, injector).pipe(mergeMap((canDeactivate) => {
            return canDeactivate && isBoolean(canDeactivate)
                ? runCanActivateChecks(targetSnapshot, canActivateChecks, injector, forwardEvent)
                : of(canDeactivate);
        }), map((guardsResult) => ({ ...t, guardsResult })));
    });
}
function runCanDeactivateChecks(checks, futureRSS, currRSS, injector) {
    return from(checks).pipe(mergeMap((check) => runCanDeactivate(check.component, check.route, currRSS, futureRSS, injector)), first((result) => {
        return result !== true;
    }, true));
}
function runCanActivateChecks(futureSnapshot, checks, injector, forwardEvent) {
    return from(checks).pipe(concatMap((check) => {
        return concat(fireChildActivationStart(check.route.parent, forwardEvent), fireActivationStart(check.route, forwardEvent), runCanActivateChild(futureSnapshot, check.path, injector), runCanActivate(futureSnapshot, check.route, injector));
    }), first((result) => {
        return result !== true;
    }, true));
}
/**
 * This should fire off `ActivationStart` events for each route being activated at this
 * level.
 * In other words, if you're activating `a` and `b` below, `path` will contain the
 * `ActivatedRouteSnapshot`s for both and we will fire `ActivationStart` for both. Always
 * return
 * `true` so checks continue to run.
 */
function fireActivationStart(snapshot, forwardEvent) {
    if (snapshot !== null && forwardEvent) {
        forwardEvent(new ActivationStart(snapshot));
    }
    return of(true);
}
/**
 * This should fire off `ChildActivationStart` events for each route being activated at this
 * level.
 * In other words, if you're activating `a` and `b` below, `path` will contain the
 * `ActivatedRouteSnapshot`s for both and we will fire `ChildActivationStart` for both. Always
 * return
 * `true` so checks continue to run.
 */
function fireChildActivationStart(snapshot, forwardEvent) {
    if (snapshot !== null && forwardEvent) {
        forwardEvent(new ChildActivationStart(snapshot));
    }
    return of(true);
}
function runCanActivate(futureRSS, futureARS, injector) {
    const canActivate = futureARS.routeConfig ? futureARS.routeConfig.canActivate : null;
    if (!canActivate || canActivate.length === 0)
        return of(true);
    const canActivateObservables = canActivate.map((canActivate) => {
        return defer(() => {
            const closestInjector = getClosestRouteInjector(futureARS) ?? injector;
            const guard = getTokenOrFunctionIdentity(canActivate, closestInjector);
            const guardVal = isCanActivate(guard)
                ? guard.canActivate(futureARS, futureRSS)
                : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));
            return wrapIntoObservable(guardVal).pipe(first());
        });
    });
    return of(canActivateObservables).pipe(prioritizedGuardValue());
}
function runCanActivateChild(futureRSS, path, injector) {
    const futureARS = path[path.length - 1];
    const canActivateChildGuards = path
        .slice(0, path.length - 1)
        .reverse()
        .map((p) => getCanActivateChild(p))
        .filter((_) => _ !== null);
    const canActivateChildGuardsMapped = canActivateChildGuards.map((d) => {
        return defer(() => {
            const guardsMapped = d.guards.map((canActivateChild) => {
                const closestInjector = getClosestRouteInjector(d.node) ?? injector;
                const guard = getTokenOrFunctionIdentity(canActivateChild, closestInjector);
                const guardVal = isCanActivateChild(guard)
                    ? guard.canActivateChild(futureARS, futureRSS)
                    : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));
                return wrapIntoObservable(guardVal).pipe(first());
            });
            return of(guardsMapped).pipe(prioritizedGuardValue());
        });
    });
    return of(canActivateChildGuardsMapped).pipe(prioritizedGuardValue());
}
function runCanDeactivate(component, currARS, currRSS, futureRSS, injector) {
    const canDeactivate = currARS && currARS.routeConfig ? currARS.routeConfig.canDeactivate : null;
    if (!canDeactivate || canDeactivate.length === 0)
        return of(true);
    const canDeactivateObservables = canDeactivate.map((c) => {
        const closestInjector = getClosestRouteInjector(currARS) ?? injector;
        const guard = getTokenOrFunctionIdentity(c, closestInjector);
        const guardVal = isCanDeactivate(guard)
            ? guard.canDeactivate(component, currARS, currRSS, futureRSS)
            : runInInjectionContext(closestInjector, () => guard(component, currARS, currRSS, futureRSS));
        return wrapIntoObservable(guardVal).pipe(first());
    });
    return of(canDeactivateObservables).pipe(prioritizedGuardValue());
}
export function runCanLoadGuards(injector, route, segments, urlSerializer) {
    const canLoad = route.canLoad;
    if (canLoad === undefined || canLoad.length === 0) {
        return of(true);
    }
    const canLoadObservables = canLoad.map((injectionToken) => {
        const guard = getTokenOrFunctionIdentity(injectionToken, injector);
        const guardVal = isCanLoad(guard)
            ? guard.canLoad(route, segments)
            : runInInjectionContext(injector, () => guard(route, segments));
        return wrapIntoObservable(guardVal);
    });
    return of(canLoadObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));
}
function redirectIfUrlTree(urlSerializer) {
    return pipe(tap((result) => {
        if (typeof result === 'boolean')
            return;
        throw redirectingNavigationError(urlSerializer, result);
    }), map((result) => result === true));
}
export function runCanMatchGuards(injector, route, segments, urlSerializer) {
    const canMatch = route.canMatch;
    if (!canMatch || canMatch.length === 0)
        return of(true);
    const canMatchObservables = canMatch.map((injectionToken) => {
        const guard = getTokenOrFunctionIdentity(injectionToken, injector);
        const guardVal = isCanMatch(guard)
            ? guard.canMatch(route, segments)
            : runInInjectionContext(injector, () => guard(route, segments));
        return wrapIntoObservable(guardVal);
    });
    return of(canMatchObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));
}
//# sourceMappingURL=data:application/json;base64,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