@use '../core/typography/typography';
@use '../core/tokens/token-utils';

@mixin base($theme) {
  // No-op
}

@mixin color($theme) {
  // No-op
}

@mixin typography($theme) {
  // No-op
}

@mixin density($theme) {
  // No-op
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: '',
      tokens: token-utils.get-overrides((base: (), color: (), typography: (), density: ()), input),
    ),
  );
}

@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  // No-op
}
