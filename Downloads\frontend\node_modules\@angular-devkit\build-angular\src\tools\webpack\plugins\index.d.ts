/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { AnyComponentStyleBudgetChecker } from './any-component-style-budget-checker';
export { ScriptsWebpackPlugin, type ScriptsWebpackPluginOptions } from './scripts-webpack-plugin';
export { SuppressExtractedTextChunksWebpackPlugin } from './suppress-entry-chunks-webpack-plugin';
export { RemoveHashPlugin, type RemoveHashPluginOptions } from './remove-hash-plugin';
export { DedupeModuleResolvePlugin } from './dedupe-module-resolve-plugin';
export { CommonJsUsageWarnPlugin } from './common-js-usage-warn-plugin';
export { JsonStatsPlugin } from './json-stats-plugin';
export { JavaScriptOptimizerPlugin } from './javascript-optimizer-plugin';
export { default as PostcssCliResources, type PostcssCliResourcesOptions, } from './postcss-cli-resources';
