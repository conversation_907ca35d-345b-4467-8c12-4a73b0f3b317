{"$schema": "http://json-schema.org/draft-07/schema", "$id": "BuildAngularWebpackServerSchema", "title": "Universal Target", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/assetPattern"}}, "main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "default": "tsconfig.app.json", "description": "The name of the TypeScript configuration file."}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking and dead-code elimination. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "x-user-analytics": "ep.ng_optimization", "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"type": "boolean", "description": "Enables optimization of the styles output.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file, in the case of production builds only.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}}, "additionalProperties": false, "required": ["outputPath", "main", "tsConfig"], "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false, "required": ["glob", "input"]}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["src", "replaceWith"]}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["replace", "with"]}]}}}