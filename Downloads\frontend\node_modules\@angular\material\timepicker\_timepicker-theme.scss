@use 'sass:map';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use './m2-timepicker';
@use './m3-timepicker';
@use '../core/tokens/token-utils';

@mixin base($theme) {
  $tokens: map.get(m2-timepicker.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-timepicker.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);
}

@mixin color($theme) {
  $tokens: map.get(m2-timepicker.get-tokens($theme), color);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-timepicker.get-tokens($theme), color);
  }

  @include token-utils.values($tokens);
}

@mixin typography($theme) {
  $tokens: map.get(m2-timepicker.get-tokens($theme), typography);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-timepicker.get-tokens($theme), typography);
  }

  @include token-utils.values($tokens);
}

@mixin density($theme) {
  $tokens: map.get(m2-timepicker.get-tokens($theme), density);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-timepicker.get-tokens($theme), density);
  }

  @include token-utils.values($tokens);
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: timepicker,
      tokens: token-utils.get-overrides(m3-timepicker.get-tokens(), timepicker)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-timepicker.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component
@mixin theme($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
