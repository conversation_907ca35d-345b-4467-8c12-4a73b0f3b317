{"version": 3, "file": "common-module-cKSwHniA.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/common-behaviors/common-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HighContrastModeDetector} from '@angular/cdk/a11y';\nimport {BidiModule} from '@angular/cdk/bidi';\nimport {inject, InjectionToken, NgModule} from '@angular/core';\nimport {_isTestEnvironment} from '@angular/cdk/platform';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nexport const MATERIAL_SANITY_CHECKS = new InjectionToken<SanityChecks>('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true,\n});\n\n/**\n * Possible sanity checks that can be enabled. If set to\n * true/false, all checks will be enabled/disabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nexport type SanityChecks = boolean | GranularSanityChecks;\n\n/**\n * Object that can be used to configure the sanity checks granularly.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nexport interface GranularSanityChecks {\n  doctype: boolean;\n  theme: boolean;\n  version: boolean;\n}\n\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\n@NgModule({\n  imports: [BidiModule],\n  exports: [BidiModule],\n})\nexport class MatCommonModule {\n  constructor(...args: any[]);\n\n  constructor() {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n}\n"], "names": [], "mappings": ";;;;;AAaA;;;;AAIG;MACU,sBAAsB,GAAG,IAAI,cAAc,CAAe,mBAAmB,EAAE;AAC1F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,IAAI;AACpB,CAAA;AAqBD;;;;;;;AAOG;MAKU,eAAe,CAAA;AAG1B,IAAA,WAAA,GAAA;;;AAGE,QAAA,MAAM,CAAC,wBAAwB,CAAC,CAAC,oCAAoC,EAAE;;uGAN9D,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAf,eAAe,EAAA,OAAA,EAAA,CAHhB,UAAU,CAAA,EAAA,OAAA,EAAA,CACV,UAAU,CAAA,EAAA,CAAA;wGAET,eAAe,EAAA,OAAA,EAAA,CAHhB,UAAU,EACV,UAAU,CAAA,EAAA,CAAA;;2FAET,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,CAAC;oBACrB,OAAO,EAAE,CAAC,UAAU,CAAC;AACtB,iBAAA;;;;;"}