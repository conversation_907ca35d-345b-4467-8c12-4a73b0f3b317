{"$schema": "http://json-schema.org/draft-07/schema", "title": "SSR Dev Server Target", "description": "SSR Dev Server target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "Browser target to build.", "pattern": ".+:.+(:.+)?"}, "serverTarget": {"type": "string", "description": "Server target to build.", "pattern": ".+:.+(:.+)?"}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "port": {"type": "number", "default": 4200, "description": "Port to start the development server at. Default is 4200. Pass 0 to get a dynamically assigned port."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "publicHost": {"type": "string", "description": "The URL that the browser client should use to connect to the development server. Use for a complex dev server setup, such as one with reverse proxies."}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "progress": {"type": "boolean", "description": "Log progress to the console while building."}, "inspect": {"type": "boolean", "description": "Launch the development server in inspector mode and listen on address and port '127.0.0.1:9229'.", "default": false}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "proxyConfig": {"type": "string", "description": "Proxy configuration file."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}}, "additionalProperties": false, "required": ["browserTarget", "serverTarget"]}