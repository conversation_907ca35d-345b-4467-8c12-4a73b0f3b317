{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/platform-browser/testing/src/browser.ts", "../../../../../../packages/platform-browser/testing/src/testing.ts", "../../../../../../packages/platform-browser/testing/public_api.ts", "../../../../../../packages/platform-browser/testing/index.ts", "../../../../../../packages/platform-browser/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {PlatformLocation} from '@angular/common';\nimport {MockPlatformLocation} from '@angular/common/testing';\nimport {\n  APP_ID,\n  createPlatformFactory,\n  NgModule,\n  PLATFORM_INITIALIZER,\n  platformCore,\n  StaticProvider,\n  ɵinternalProvideZoneChangeDetection as internalProvideZoneChangeDetection,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  ɵChangeDetectionSchedulerImpl as ChangeDetectionSchedulerImpl,\n} from '@angular/core';\nimport {BrowserModule, ɵBrowserDomAdapter as BrowserDomAdapter} from '@angular/platform-browser';\n\nfunction initBrowserTests() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nconst _TEST_BROWSER_PLATFORM_PROVIDERS: StaticProvider[] = [\n  {provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true},\n];\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nexport const platformBrowserTesting = createPlatformFactory(\n  platformCore,\n  'browserTesting',\n  _TEST_BROWSER_PLATFORM_PROVIDERS,\n);\n\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    internalProvideZoneChangeDetection({}),\n    {provide: ChangeDetectionScheduler, useExisting: ChangeDetectionSchedulerImpl},\n    {provide: PlatformLocation, useClass: MockPlatformLocation},\n  ],\n})\nexport class BrowserTestingModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\nexport * from './browser';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/// <reference types=\"jasmine\" />\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["BrowserDomAdapter", "internalProvideZoneChangeDetection", "ChangeDetectionScheduler", "ChangeDetectionSchedulerImpl"], "mappings": ";;;;;;;;;;;;AAsBA,SAAS,gBAAgB,GAAA;IACvBA,kBAAiB,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AAED,MAAM,gCAAgC,GAAqB;IACzD,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAC;CACzE,CAAC;AAEF;;;;AAIG;AACI,MAAM,sBAAsB,GAAG,qBAAqB,CACzD,YAAY,EACZ,gBAAgB,EAChB,gCAAgC,EAChC;AAEF;;;;AAIG;MAUU,oBAAoB,CAAA;yHAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YARrB,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;AAQZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAPpB,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;YAChCC,mCAAkC,CAAC,EAAE,CAAC;AACtC,YAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,WAAW,EAAEC,6BAA4B,EAAC;AAC9E,YAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC5D,SAAA,EAAA,OAAA,EAAA,CANS,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAQZ,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAThC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;wBAChCF,mCAAkC,CAAC,EAAE,CAAC;AACtC,wBAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,WAAW,EAAEC,6BAA4B,EAAC;AAC9E,wBAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC5D,qBAAA;AACF,iBAAA,CAAA;;;AC9CD;;;;AAIG;;ACJH;;ACAA;;ACRA;;AAEG;;;;"}