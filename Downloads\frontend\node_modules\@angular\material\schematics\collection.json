{"$schema": "./node_modules/@angular-devkit/schematics/collection-schema.json", "schematics": {"ng-add": {"description": "Adds Angular Material to the application without affecting any templates", "factory": "./ng-add/index", "schema": "./ng-add/schema.json", "aliases": ["material-shell", "install"], "hidden": true}, "ng-add-setup-project": {"description": "Sets up the specified project after the ng-add dependencies have been installed.", "private": true, "factory": "./ng-add/setup-project", "schema": "./ng-add/schema.json", "hidden": true}, "dashboard": {"description": "Create a card-based dashboard component", "factory": "./ng-generate/dashboard/index", "schema": "./ng-generate/dashboard/schema.json", "aliases": ["material-dashboard"]}, "table": {"description": "Create a component that displays data with a data-table", "factory": "./ng-generate/table/index", "schema": "./ng-generate/table/schema.json", "aliases": ["material-table"]}, "navigation": {"description": "Create a component with a responsive sidenav for navigation", "factory": "./ng-generate/navigation/index", "schema": "./ng-generate/navigation/schema.json", "aliases": ["material-nav", "materialNav", "nav"]}, "tree": {"description": "Create a file tree component.", "factory": "./ng-generate/tree/index", "schema": "./ng-generate/tree/schema.json", "aliases": ["material-tree"]}, "addressForm": {"description": "Create a component with an address form", "factory": "./ng-generate/address-form/index", "schema": "./ng-generate/address-form/schema.json", "aliases": ["address-form", "material-address-form", "material-addressForm"]}, "m3Theme": {"description": "Generate M3 theme", "factory": "./ng-generate/theme-color/index_bundled", "schema": "./ng-generate/theme-color/schema.json", "aliases": ["theme-color"]}}}