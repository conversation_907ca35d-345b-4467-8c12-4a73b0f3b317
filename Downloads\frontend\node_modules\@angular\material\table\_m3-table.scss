@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-table.
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  @return (
    base: (
      table-row-item-outline-width: 1px,
    ),
    color: (
      table-background-color: map.get($system, surface),
      table-header-headline-color: map.get($system, on-surface),
      table-row-item-label-text-color: map.get($system, on-surface),
      table-row-item-outline-color: map.get($system, outline),
    ),
    typography: (
      table-header-headline-font: map.get($system, title-small-font),
      table-header-headline-line-height: map.get($system, title-small-line-height),
      table-header-headline-size: map.get($system, title-small-size),
      table-header-headline-tracking: map.get($system, title-small-tracking),
      table-header-headline-weight: map.get($system, title-small-weight),
      table-row-item-label-text-font: map.get($system, body-medium-font),
      table-row-item-label-text-line-height: map.get($system, body-medium-line-height),
      table-row-item-label-text-size: map.get($system, body-medium-size),
      table-row-item-label-text-tracking: map.get($system, body-medium-tracking),
      table-row-item-label-text-weight: map.get($system, body-medium-weight),
      table-footer-supporting-text-font: map.get($system, body-medium-font),
      table-footer-supporting-text-line-height: map.get($system, body-medium-line-height),
      table-footer-supporting-text-size: map.get($system, body-medium-size),
      table-footer-supporting-text-tracking: map.get($system, body-medium-tracking),
      table-footer-supporting-text-weight: map.get($system, body-medium-weight),
    ),
    density: get-density-tokens(map.get($system, density-scale)),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -4);
  $index: ($scale * -1) + 1;

  @return (
    table-header-container-height: list.nth((56px, 52px, 48px, 44px, 40px), $index),
    table-footer-container-height: list.nth((52px, 48px, 44px, 40px, 36px), $index),
    table-row-item-container-height: list.nth((52px, 48px, 44px, 40px, 36px), $index),
  );
}
