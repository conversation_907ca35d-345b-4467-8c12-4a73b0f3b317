/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * Can be returned by a `Router` guard to instruct the `Router` to redirect rather than continue
 * processing the path of the in-flight navigation. The `redirectTo` indicates _where_ the new
 * navigation should go to and the optional `navigationBehaviorOptions` can provide more information
 * about _how_ to perform the navigation.
 *
 * ```ts
 * const route: Route = {
 *   path: "user/:userId",
 *   component: User,
 *   canActivate: [
 *     () => {
 *       const router = inject(Router);
 *       const authService = inject(AuthenticationService);
 *
 *       if (!authService.isLoggedIn()) {
 *         const loginPath = router.parseUrl("/login");
 *         return new RedirectCommand(loginPath, {
 *           skipLocationChange: "true",
 *         });
 *       }
 *
 *       return true;
 *     },
 *   ],
 * };
 * ```
 * @see [Routing guide](guide/routing/common-router-tasks#preventing-unauthorized-access)
 *
 * @publicApi
 */
export class RedirectCommand {
    constructor(redirectTo, navigationBehaviorOptions) {
        this.redirectTo = redirectTo;
        this.navigationBehaviorOptions = navigationBehaviorOptions;
    }
}
//# sourceMappingURL=data:application/json;base64,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