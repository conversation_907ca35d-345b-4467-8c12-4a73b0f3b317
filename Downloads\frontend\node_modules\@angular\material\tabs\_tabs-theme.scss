@use './m2-tabs';
@use './m3-tabs';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use '../core/tokens/token-utils';
@use 'sass:map';

@mixin base($theme) {
  $tokens: map.get(m2-tabs.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-tabs.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);
}

/// Outputs color theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-tabs.get-tokens($theme, $color-variant), color);
    @include token-utils.values($tokens);
  } @else {
    .mat-mdc-tab-group,
    .mat-mdc-tab-nav-bar {
      @include token-utils.values(
          m2-tabs.private-get-color-palette-color-tokens($theme, primary,
            $exclude: (tab-background-color, tab-foreground-color)));

      &.mat-accent {
        @include token-utils.values(
          m2-tabs.private-get-color-palette-color-tokens($theme, secondary,
            $exclude: (tab-background-color, tab-foreground-color)));
      }

      &.mat-warn {
        @include token-utils.values(
          m2-tabs.private-get-color-palette-color-tokens($theme, error,
            $exclude: (tab-background-color, tab-foreground-color)));
      }

      &.mat-background-primary {
        $tokens: m2-tabs.private-get-color-palette-color-tokens($theme, primary);
        @include token-utils.values((
          tab-background-color: map.get($tokens, tab-background-color),
          tab-foreground-color: map.get($tokens, tab-foreground-color)
        ));
      }

      &.mat-background-accent {
        $tokens: m2-tabs.private-get-color-palette-color-tokens($theme, secondary);
        @include token-utils.values((
          tab-background-color: map.get($tokens, tab-background-color),
          tab-foreground-color: map.get($tokens, tab-foreground-color),
        ));
      }

      &.mat-background-warn {
        $tokens: m2-tabs.private-get-color-palette-color-tokens($theme, error);
        @include token-utils.values((
          tab-background-color: map.get($tokens, tab-background-color),
          tab-foreground-color: map.get($tokens, tab-foreground-color),
        ));
      }
    }
  }
}

/// Outputs typography theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.values(map.get(m3-tabs.get-tokens($theme), typography));
  } @else {
    .mat-mdc-tab-header {
      @include token-utils.values(map.get(m2-tabs.get-tokens($theme), typography));
    }
  }
}

/// Outputs density theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.values(map.get(m3-tabs.get-tokens($theme), density));
  } @else {
    .mat-mdc-tab-header {
      @include token-utils.values(map.get(m2-tabs.get-tokens($theme), density));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tab,
      tokens: token-utils.get-overrides(m3-tabs.get-tokens(), tab)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-tab.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme, $color-variant);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
