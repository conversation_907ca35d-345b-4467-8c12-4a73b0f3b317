/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { EmptyError } from 'rxjs';
/**
 * Simple function check, but generic so type inference will flow. Example:
 *
 * function product(a: number, b: number) {
 *   return a * b;
 * }
 *
 * if (isFunction<product>(fn)) {
 *   return fn(1, 2);
 * } else {
 *   throw "Must provide the `product` function";
 * }
 */
export function isFunction(v) {
    return typeof v === 'function';
}
export function isBoolean(v) {
    return typeof v === 'boolean';
}
export function isCanLoad(guard) {
    return guard && isFunction(guard.canLoad);
}
export function isCanActivate(guard) {
    return guard && isFunction(guard.canActivate);
}
export function isCanActivateChild(guard) {
    return guard && isFunction(guard.canActivateChild);
}
export function isCanDeactivate(guard) {
    return guard && isFunction(guard.canDeactivate);
}
export function isCanMatch(guard) {
    return guard && isFunction(guard.canMatch);
}
export function isEmptyError(e) {
    return e instanceof EmptyError || e?.name === 'EmptyError';
}
//# sourceMappingURL=data:application/json;base64,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