"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadProxyConfiguration = exports.deleteOutputDir = void 0;
__exportStar(require("./default-progress"), exports);
var private_1 = require("@angular/build/private");
Object.defineProperty(exports, "deleteOutputDir", { enumerable: true, get: function () { return private_1.deleteOutputDir; } });
Object.defineProperty(exports, "loadProxyConfiguration", { enumerable: true, get: function () { return private_1.loadProxyConfiguration; } });
__exportStar(require("./run-module-as-observable-fork"), exports);
__exportStar(require("./normalize-file-replacements"), exports);
__exportStar(require("./normalize-asset-patterns"), exports);
__exportStar(require("./normalize-source-maps"), exports);
__exportStar(require("./normalize-optimization"), exports);
__exportStar(require("./normalize-builder-schema"), exports);
__exportStar(require("./url"), exports);
