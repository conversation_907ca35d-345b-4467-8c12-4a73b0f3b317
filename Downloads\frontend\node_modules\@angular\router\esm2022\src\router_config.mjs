/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { InjectionToken } from '@angular/core';
/**
 * A DI token for the router service.
 *
 * @publicApi
 */
export const ROUTER_CONFIGURATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router config' : '', {
    providedIn: 'root',
    factory: () => ({}),
});
//# sourceMappingURL=data:application/json;base64,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