import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { UserListComponent } from './user-list/user-list.component';
import { TeamListComponent } from './team-list/team-list.component';
import { AbsenceListComponent } from './absence-list/absence-list.component';
import { AuthGuard } from './auth.guard';
import { DashboardComponent } from './dashboard/dashboard.component';

export const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },

  // Dashboard
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },

  // Users
  { path: 'users', component: UserListComponent, canActivate: [AuthGuard] },

  // Teams
  { path: 'teams', component: TeamListComponent, canActivate: [AuthGuard] },

  // Absences
  { path: 'absences', component: AbsenceListComponent, canActivate: [AuthGuard] },

  // Wildcard route
  { path: '**', redirectTo: '/login' }
];