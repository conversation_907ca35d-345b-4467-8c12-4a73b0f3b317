/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * Entry point for all animation APIs of the animation browser package.
 */
export { ANIMATION_MODULE_TYPE } from '@angular/core';
export { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, } from './module';
export * from './private_export';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYW5pbWF0aW9ucy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL3BsYXRmb3JtLWJyb3dzZXIvYW5pbWF0aW9ucy9zcmMvYW5pbWF0aW9ucy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSDs7OztHQUlHO0FBQ0gsT0FBTyxFQUFDLHFCQUFxQixFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3BELE9BQU8sRUFDTCx1QkFBdUIsRUFFdkIsb0JBQW9CLEVBQ3BCLGlCQUFpQixFQUNqQixxQkFBcUIsR0FDdEIsTUFBTSxVQUFVLENBQUM7QUFFbEIsY0FBYyxrQkFBa0IsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmRldi9saWNlbnNlXG4gKi9cblxuLyoqXG4gKiBAbW9kdWxlXG4gKiBAZGVzY3JpcHRpb25cbiAqIEVudHJ5IHBvaW50IGZvciBhbGwgYW5pbWF0aW9uIEFQSXMgb2YgdGhlIGFuaW1hdGlvbiBicm93c2VyIHBhY2thZ2UuXG4gKi9cbmV4cG9ydCB7QU5JTUFUSU9OX01PRFVMRV9UWVBFfSBmcm9tICdAYW5ndWxhci9jb3JlJztcbmV4cG9ydCB7XG4gIEJyb3dzZXJBbmltYXRpb25zTW9kdWxlLFxuICBCcm93c2VyQW5pbWF0aW9uc01vZHVsZUNvbmZpZyxcbiAgTm9vcEFuaW1hdGlvbnNNb2R1bGUsXG4gIHByb3ZpZGVBbmltYXRpb25zLFxuICBwcm92aWRlTm9vcEFuaW1hdGlvbnMsXG59IGZyb20gJy4vbW9kdWxlJztcblxuZXhwb3J0ICogZnJvbSAnLi9wcml2YXRlX2V4cG9ydCc7XG4iXX0=