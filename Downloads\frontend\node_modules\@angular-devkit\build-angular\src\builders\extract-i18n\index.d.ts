/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { execute } from './builder';
import type { Schema as ExtractI18nBuilderOptions } from './schema';
export { ExtractI18nBuilderOptions, execute };
declare const _default: import("../../../../architect/src/internal").Builder<ExtractI18nBuilderOptions & import("../../../../core/src").JsonObject>;
export default _default;
