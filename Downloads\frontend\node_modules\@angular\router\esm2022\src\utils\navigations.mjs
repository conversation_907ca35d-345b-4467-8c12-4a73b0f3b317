/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { filter, map, take } from 'rxjs/operators';
import { NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, } from '../events';
var NavigationResult;
(function (NavigationResult) {
    NavigationResult[NavigationResult["COMPLETE"] = 0] = "COMPLETE";
    NavigationResult[NavigationResult["FAILED"] = 1] = "FAILED";
    NavigationResult[NavigationResult["REDIRECTING"] = 2] = "REDIRECTING";
})(NavigationResult || (NavigationResult = {}));
/**
 * Performs the given action once the router finishes its next/current navigation.
 *
 * The navigation is considered complete under the following conditions:
 * - `NavigationCancel` event emits and the code is not `NavigationCancellationCode.Redirect` or
 * `NavigationCancellationCode.SupersededByNewNavigation`. In these cases, the
 * redirecting/superseding navigation must finish.
 * - `NavigationError`, `NavigationEnd`, or `NavigationSkipped` event emits
 */
export function afterNextNavigation(router, action) {
    router.events
        .pipe(filter((e) => e instanceof NavigationEnd ||
        e instanceof NavigationCancel ||
        e instanceof NavigationError ||
        e instanceof NavigationSkipped), map((e) => {
        if (e instanceof NavigationEnd || e instanceof NavigationSkipped) {
            return NavigationResult.COMPLETE;
        }
        const redirecting = e instanceof NavigationCancel
            ? e.code === NavigationCancellationCode.Redirect ||
                e.code === NavigationCancellationCode.SupersededByNewNavigation
            : false;
        return redirecting ? NavigationResult.REDIRECTING : NavigationResult.FAILED;
    }), filter((result) => result !== NavigationResult.REDIRECTING), take(1))
        .subscribe(() => {
        action();
    });
}
//# sourceMappingURL=data:application/json;base64,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