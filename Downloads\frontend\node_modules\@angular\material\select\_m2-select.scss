@use 'sass:map';
@use '../core/theming/theming';
@use '../core/style/elevation';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -2);

  @return (
    base: (
      select-container-elevation-shadow: elevation.get-box-shadow(8),
    ),
    color: private-get-color-palette-color-tokens($theme, primary),
    typography: (
      select-trigger-text-font: map.get($system, body-large-font),
      select-trigger-text-line-height: map.get($system, body-large-line-height),
      select-trigger-text-size: map.get($system, body-large-size),
      select-trigger-text-tracking: map.get($system, body-large-tracking),
      select-trigger-text-weight: map.get($system, body-large-weight)
    ),
    density: (
      select-arrow-transform: map.get((
        0: translateY(-8px),
        -1: translateY(-8px),
        -2: none,
      ), $density-scale),
    ),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
  $disabled: m3-utils.color-with-opacity(map.get($system, on-surface), 38%);

  @return (
    select-panel-background-color: map.get($system, surface),
    select-enabled-trigger-text-color: map.get($system, on-surface),
    select-disabled-trigger-text-color: $disabled,
    select-placeholder-text-color: map.get($system, on-surface-variant),
    select-enabled-arrow-color: map.get($system, on-surface-variant),
    select-disabled-arrow-color: $disabled,
    select-focused-arrow-color: map.get($system, primary),
    select-invalid-arrow-color: map.get($system, error),
  );
}
