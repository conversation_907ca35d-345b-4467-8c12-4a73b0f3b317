/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { createUrlTreeFromSnapshot } from './create_url_tree';
export { RouterLink, RouterLinkWithHref } from './directives/router_link';
export { RouterLinkActive } from './directives/router_link_active';
export { RouterOutlet } from './directives/router_outlet';
export { ActivationEnd, ActivationStart, ChildActivationEnd, ChildActivationStart, EventType, GuardsCheckEnd, GuardsCheckStart, NavigationCancel, NavigationCancellationCode as NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RouterEvent, RoutesRecognized, Scroll, } from './events';
export { RedirectCommand, } from './models';
export * from './models_deprecated';
export { DefaultTitleStrategy, TitleStrategy } from './page_title_strategy';
export { withViewTransitions, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, } from './provide_router';
export { BaseRouteReuseStrategy, RouteReuseStrategy, } from './route_reuse_strategy';
export { Router } from './router';
export { ROUTER_CONFIGURATION, } from './router_config';
export { ROUTES } from './router_config_loader';
export { ROUTER_INITIALIZER, RouterModule } from './router_module';
export { ChildrenOutletContexts, OutletContext } from './router_outlet_context';
export { NoPreloading, PreloadAllModules, PreloadingStrategy, RouterPreloader, } from './router_preloader';
export { ActivatedRoute, ActivatedRouteSnapshot, RouterState, RouterStateSnapshot, } from './router_state';
export { convertToParamMap, defaultUrlMatcher, PRIMARY_OUTLET } from './shared';
export { UrlHandlingStrategy } from './url_handling_strategy';
export { DefaultUrlSerializer, UrlSegment, UrlSegmentGroup, UrlSerializer, UrlTree, } from './url_tree';
export { mapToCanActivate, mapToCanActivateChild, mapToCanDeactivate, mapToCanMatch, mapToResolve, } from './utils/functional_guards';
export { VERSION } from './version';
export * from './private_export';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9yb3V0ZXIvc3JjL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyx5QkFBeUIsRUFBQyxNQUFNLG1CQUFtQixDQUFDO0FBQzVELE9BQU8sRUFBQyxVQUFVLEVBQUUsa0JBQWtCLEVBQUMsTUFBTSwwQkFBMEIsQ0FBQztBQUN4RSxPQUFPLEVBQUMsZ0JBQWdCLEVBQUMsTUFBTSxpQ0FBaUMsQ0FBQztBQUNqRSxPQUFPLEVBQUMsWUFBWSxFQUF1QixNQUFNLDRCQUE0QixDQUFDO0FBQzlFLE9BQU8sRUFDTCxhQUFhLEVBQ2IsZUFBZSxFQUNmLGtCQUFrQixFQUNsQixvQkFBb0IsRUFFcEIsU0FBUyxFQUNULGNBQWMsRUFDZCxnQkFBZ0IsRUFDaEIsZ0JBQWdCLEVBQ2hCLDBCQUEwQixJQUFJLDBCQUEwQixFQUN4RCxhQUFhLEVBQ2IsZUFBZSxFQUNmLGlCQUFpQixFQUNqQixxQkFBcUIsRUFDckIsZUFBZSxFQUNmLFVBQVUsRUFDVixZQUFZLEVBQ1osa0JBQWtCLEVBQ2xCLG9CQUFvQixFQUNwQixXQUFXLEVBQ1gsZ0JBQWdCLEVBQ2hCLE1BQU0sR0FDUCxNQUFNLFVBQVUsQ0FBQztBQUNsQixPQUFPLEVBdUJMLGVBQWUsR0FPaEIsTUFBTSxVQUFVLENBQUM7QUFHbEIsY0FBYyxxQkFBcUIsQ0FBQztBQUVwQyxPQUFPLEVBQUMsb0JBQW9CLEVBQUUsYUFBYSxFQUFDLE1BQU0sdUJBQXVCLENBQUM7QUFDMUUsT0FBTyxFQUdMLG1CQUFtQixFQU9uQixhQUFhLEVBQ2IsYUFBYSxFQUtiLHlCQUF5QixFQUN6QixnQkFBZ0IsRUFDaEIsNkJBQTZCLEVBQzdCLG9DQUFvQyxFQUNwQyxnQkFBZ0IsRUFDaEIscUJBQXFCLEVBQ3JCLDBCQUEwQixFQUMxQixjQUFjLEVBQ2QsZ0JBQWdCLEdBQ2pCLE1BQU0sa0JBQWtCLENBQUM7QUFDMUIsT0FBTyxFQUNMLHNCQUFzQixFQUV0QixrQkFBa0IsR0FDbkIsTUFBTSx3QkFBd0IsQ0FBQztBQUNoQyxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sVUFBVSxDQUFDO0FBQ2hDLE9BQU8sRUFJTCxvQkFBb0IsR0FFckIsTUFBTSxpQkFBaUIsQ0FBQztBQUN6QixPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sd0JBQXdCLENBQUM7QUFDOUMsT0FBTyxFQUFDLGtCQUFrQixFQUFFLFlBQVksRUFBQyxNQUFNLGlCQUFpQixDQUFDO0FBQ2pFLE9BQU8sRUFBQyxzQkFBc0IsRUFBRSxhQUFhLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUM5RSxPQUFPLEVBQ0wsWUFBWSxFQUNaLGlCQUFpQixFQUNqQixrQkFBa0IsRUFDbEIsZUFBZSxHQUNoQixNQUFNLG9CQUFvQixDQUFDO0FBQzVCLE9BQU8sRUFDTCxjQUFjLEVBQ2Qsc0JBQXNCLEVBQ3RCLFdBQVcsRUFDWCxtQkFBbUIsR0FDcEIsTUFBTSxnQkFBZ0IsQ0FBQztBQUN4QixPQUFPLEVBQUMsaUJBQWlCLEVBQUUsaUJBQWlCLEVBQW9CLGNBQWMsRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUNoRyxPQUFPLEVBQUMsbUJBQW1CLEVBQUMsTUFBTSx5QkFBeUIsQ0FBQztBQUM1RCxPQUFPLEVBQ0wsb0JBQW9CLEVBRXBCLFVBQVUsRUFDVixlQUFlLEVBQ2YsYUFBYSxFQUNiLE9BQU8sR0FDUixNQUFNLFlBQVksQ0FBQztBQUNwQixPQUFPLEVBQ0wsZ0JBQWdCLEVBQ2hCLHFCQUFxQixFQUNyQixrQkFBa0IsRUFDbEIsYUFBYSxFQUNiLFlBQVksR0FDYixNQUFNLDJCQUEyQixDQUFDO0FBQ25DLE9BQU8sRUFBQyxPQUFPLEVBQUMsTUFBTSxXQUFXLENBQUM7QUFFbEMsY0FBYyxrQkFBa0IsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmRldi9saWNlbnNlXG4gKi9cblxuZXhwb3J0IHtjcmVhdGVVcmxUcmVlRnJvbVNuYXBzaG90fSBmcm9tICcuL2NyZWF0ZV91cmxfdHJlZSc7XG5leHBvcnQge1JvdXRlckxpbmssIFJvdXRlckxpbmtXaXRoSHJlZn0gZnJvbSAnLi9kaXJlY3RpdmVzL3JvdXRlcl9saW5rJztcbmV4cG9ydCB7Um91dGVyTGlua0FjdGl2ZX0gZnJvbSAnLi9kaXJlY3RpdmVzL3JvdXRlcl9saW5rX2FjdGl2ZSc7XG5leHBvcnQge1JvdXRlck91dGxldCwgUm91dGVyT3V0bGV0Q29udHJhY3R9IGZyb20gJy4vZGlyZWN0aXZlcy9yb3V0ZXJfb3V0bGV0JztcbmV4cG9ydCB7XG4gIEFjdGl2YXRpb25FbmQsXG4gIEFjdGl2YXRpb25TdGFydCxcbiAgQ2hpbGRBY3RpdmF0aW9uRW5kLFxuICBDaGlsZEFjdGl2YXRpb25TdGFydCxcbiAgRXZlbnQsXG4gIEV2ZW50VHlwZSxcbiAgR3VhcmRzQ2hlY2tFbmQsXG4gIEd1YXJkc0NoZWNrU3RhcnQsXG4gIE5hdmlnYXRpb25DYW5jZWwsXG4gIE5hdmlnYXRpb25DYW5jZWxsYXRpb25Db2RlIGFzIE5hdmlnYXRpb25DYW5jZWxsYXRpb25Db2RlLFxuICBOYXZpZ2F0aW9uRW5kLFxuICBOYXZpZ2F0aW9uRXJyb3IsXG4gIE5hdmlnYXRpb25Ta2lwcGVkLFxuICBOYXZpZ2F0aW9uU2tpcHBlZENvZGUsXG4gIE5hdmlnYXRpb25TdGFydCxcbiAgUmVzb2x2ZUVuZCxcbiAgUmVzb2x2ZVN0YXJ0LFxuICBSb3V0ZUNvbmZpZ0xvYWRFbmQsXG4gIFJvdXRlQ29uZmlnTG9hZFN0YXJ0LFxuICBSb3V0ZXJFdmVudCxcbiAgUm91dGVzUmVjb2duaXplZCxcbiAgU2Nyb2xsLFxufSBmcm9tICcuL2V2ZW50cyc7XG5leHBvcnQge1xuICBDYW5BY3RpdmF0ZUNoaWxkRm4sXG4gIE1heWJlQXN5bmMsXG4gIEd1YXJkUmVzdWx0LFxuICBDYW5BY3RpdmF0ZUZuLFxuICBDYW5EZWFjdGl2YXRlRm4sXG4gIENhbkxvYWRGbixcbiAgQ2FuTWF0Y2hGbixcbiAgRGF0YSxcbiAgRGVmYXVsdEV4cG9ydCxcbiAgTG9hZENoaWxkcmVuLFxuICBMb2FkQ2hpbGRyZW5DYWxsYmFjayxcbiAgTmF2aWdhdGlvbkJlaGF2aW9yT3B0aW9ucyxcbiAgT25TYW1lVXJsTmF2aWdhdGlvbixcbiAgUXVlcnlQYXJhbXNIYW5kbGluZyxcbiAgUmVkaXJlY3RGdW5jdGlvbixcbiAgUmVzb2x2ZURhdGEsXG4gIFJlc29sdmVGbixcbiAgUm91dGUsXG4gIFJvdXRlcyxcbiAgUnVuR3VhcmRzQW5kUmVzb2x2ZXJzLFxuICBVcmxNYXRjaGVyLFxuICBVcmxNYXRjaFJlc3VsdCxcbiAgUmVkaXJlY3RDb21tYW5kLFxuICBDYW5BY3RpdmF0ZSxcbiAgQ2FuQWN0aXZhdGVDaGlsZCxcbiAgQ2FuRGVhY3RpdmF0ZSxcbiAgQ2FuTG9hZCxcbiAgQ2FuTWF0Y2gsXG4gIFJlc29sdmUsXG59IGZyb20gJy4vbW9kZWxzJztcbmV4cG9ydCB7Vmlld1RyYW5zaXRpb25JbmZvLCBWaWV3VHJhbnNpdGlvbnNGZWF0dXJlT3B0aW9uc30gZnJvbSAnLi91dGlscy92aWV3X3RyYW5zaXRpb24nO1xuXG5leHBvcnQgKiBmcm9tICcuL21vZGVsc19kZXByZWNhdGVkJztcbmV4cG9ydCB7TmF2aWdhdGlvbiwgTmF2aWdhdGlvbkV4dHJhcywgVXJsQ3JlYXRpb25PcHRpb25zfSBmcm9tICcuL25hdmlnYXRpb25fdHJhbnNpdGlvbic7XG5leHBvcnQge0RlZmF1bHRUaXRsZVN0cmF0ZWd5LCBUaXRsZVN0cmF0ZWd5fSBmcm9tICcuL3BhZ2VfdGl0bGVfc3RyYXRlZ3knO1xuZXhwb3J0IHtcbiAgRGVidWdUcmFjaW5nRmVhdHVyZSxcbiAgRGlzYWJsZWRJbml0aWFsTmF2aWdhdGlvbkZlYXR1cmUsXG4gIHdpdGhWaWV3VHJhbnNpdGlvbnMsXG4gIFZpZXdUcmFuc2l0aW9uc0ZlYXR1cmUsXG4gIEVuYWJsZWRCbG9ja2luZ0luaXRpYWxOYXZpZ2F0aW9uRmVhdHVyZSxcbiAgSW5pdGlhbE5hdmlnYXRpb25GZWF0dXJlLFxuICBJbk1lbW9yeVNjcm9sbGluZ0ZlYXR1cmUsXG4gIE5hdmlnYXRpb25FcnJvckhhbmRsZXJGZWF0dXJlLFxuICBQcmVsb2FkaW5nRmVhdHVyZSxcbiAgcHJvdmlkZVJvdXRlcixcbiAgcHJvdmlkZVJvdXRlcyxcbiAgUm91dGVyQ29uZmlndXJhdGlvbkZlYXR1cmUsXG4gIFJvdXRlckZlYXR1cmUsXG4gIFJvdXRlckZlYXR1cmVzLFxuICBSb3V0ZXJIYXNoTG9jYXRpb25GZWF0dXJlLFxuICB3aXRoQ29tcG9uZW50SW5wdXRCaW5kaW5nLFxuICB3aXRoRGVidWdUcmFjaW5nLFxuICB3aXRoRGlzYWJsZWRJbml0aWFsTmF2aWdhdGlvbixcbiAgd2l0aEVuYWJsZWRCbG9ja2luZ0luaXRpYWxOYXZpZ2F0aW9uLFxuICB3aXRoSGFzaExvY2F0aW9uLFxuICB3aXRoSW5NZW1vcnlTY3JvbGxpbmcsXG4gIHdpdGhOYXZpZ2F0aW9uRXJyb3JIYW5kbGVyLFxuICB3aXRoUHJlbG9hZGluZyxcbiAgd2l0aFJvdXRlckNvbmZpZyxcbn0gZnJvbSAnLi9wcm92aWRlX3JvdXRlcic7XG5leHBvcnQge1xuICBCYXNlUm91dGVSZXVzZVN0cmF0ZWd5LFxuICBEZXRhY2hlZFJvdXRlSGFuZGxlLFxuICBSb3V0ZVJldXNlU3RyYXRlZ3ksXG59IGZyb20gJy4vcm91dGVfcmV1c2Vfc3RyYXRlZ3knO1xuZXhwb3J0IHtSb3V0ZXJ9IGZyb20gJy4vcm91dGVyJztcbmV4cG9ydCB7XG4gIEV4dHJhT3B0aW9ucyxcbiAgSW5pdGlhbE5hdmlnYXRpb24sXG4gIEluTWVtb3J5U2Nyb2xsaW5nT3B0aW9ucyxcbiAgUk9VVEVSX0NPTkZJR1VSQVRJT04sXG4gIFJvdXRlckNvbmZpZ09wdGlvbnMsXG59IGZyb20gJy4vcm91dGVyX2NvbmZpZyc7XG5leHBvcnQge1JPVVRFU30gZnJvbSAnLi9yb3V0ZXJfY29uZmlnX2xvYWRlcic7XG5leHBvcnQge1JPVVRFUl9JTklUSUFMSVpFUiwgUm91dGVyTW9kdWxlfSBmcm9tICcuL3JvdXRlcl9tb2R1bGUnO1xuZXhwb3J0IHtDaGlsZHJlbk91dGxldENvbnRleHRzLCBPdXRsZXRDb250ZXh0fSBmcm9tICcuL3JvdXRlcl9vdXRsZXRfY29udGV4dCc7XG5leHBvcnQge1xuICBOb1ByZWxvYWRpbmcsXG4gIFByZWxvYWRBbGxNb2R1bGVzLFxuICBQcmVsb2FkaW5nU3RyYXRlZ3ksXG4gIFJvdXRlclByZWxvYWRlcixcbn0gZnJvbSAnLi9yb3V0ZXJfcHJlbG9hZGVyJztcbmV4cG9ydCB7XG4gIEFjdGl2YXRlZFJvdXRlLFxuICBBY3RpdmF0ZWRSb3V0ZVNuYXBzaG90LFxuICBSb3V0ZXJTdGF0ZSxcbiAgUm91dGVyU3RhdGVTbmFwc2hvdCxcbn0gZnJvbSAnLi9yb3V0ZXJfc3RhdGUnO1xuZXhwb3J0IHtjb252ZXJ0VG9QYXJhbU1hcCwgZGVmYXVsdFVybE1hdGNoZXIsIFBhcmFtTWFwLCBQYXJhbXMsIFBSSU1BUllfT1VUTEVUfSBmcm9tICcuL3NoYXJlZCc7XG5leHBvcnQge1VybEhhbmRsaW5nU3RyYXRlZ3l9IGZyb20gJy4vdXJsX2hhbmRsaW5nX3N0cmF0ZWd5JztcbmV4cG9ydCB7XG4gIERlZmF1bHRVcmxTZXJpYWxpemVyLFxuICBJc0FjdGl2ZU1hdGNoT3B0aW9ucyxcbiAgVXJsU2VnbWVudCxcbiAgVXJsU2VnbWVudEdyb3VwLFxuICBVcmxTZXJpYWxpemVyLFxuICBVcmxUcmVlLFxufSBmcm9tICcuL3VybF90cmVlJztcbmV4cG9ydCB7XG4gIG1hcFRvQ2FuQWN0aXZhdGUsXG4gIG1hcFRvQ2FuQWN0aXZhdGVDaGlsZCxcbiAgbWFwVG9DYW5EZWFjdGl2YXRlLFxuICBtYXBUb0Nhbk1hdGNoLFxuICBtYXBUb1Jlc29sdmUsXG59IGZyb20gJy4vdXRpbHMvZnVuY3Rpb25hbF9ndWFyZHMnO1xuZXhwb3J0IHtWRVJTSU9OfSBmcm9tICcuL3ZlcnNpb24nO1xuXG5leHBvcnQgKiBmcm9tICcuL3ByaXZhdGVfZXhwb3J0JztcbiJdfQ==