@use '../core/tokens/m2-utils';
@use 'sass:map';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (),
    color: (),
    typography: (
      // TODO(crisbeto): other components have tokens for all typography dimensions.
      // Here we only set the font size to maintain the same appearance as the pre-tokens
      // theming API. Consider adding more tokens for letter spacing, font weight etc.
      grid-list-tile-header-primary-text-size: map.get($system, body-medium-size),
      grid-list-tile-header-secondary-text-size: map.get($system, body-small-size),
      grid-list-tile-footer-primary-text-size: map.get($system, body-medium-size),
      grid-list-tile-footer-secondary-text-size: map.get($system, body-small-size),
    ),
    density: (),
  );
}
