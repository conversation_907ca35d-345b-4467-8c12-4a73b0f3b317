@use 'sass:map';
@use './m2-menu';
@use './m3-menu';
@use '../core/tokens/token-utils';
@use '../core/theming/inspection';
@use '../core/typography/typography';

@mixin base($theme) {
  $tokens: map.get(m2-menu.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-menu.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);
}

@mixin color($theme) {
  $tokens: map.get(m2-menu.get-tokens($theme), color);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-menu.get-tokens($theme), color);
  }

  @include token-utils.values($tokens);
}

@mixin typography($theme) {
  $tokens: map.get(m2-menu.get-tokens($theme), typography);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-menu.get-tokens($theme), typography);
  }

  @include token-utils.values($tokens);
}

@mixin density($theme) {
  $tokens: map.get(m2-menu.get-tokens($theme), density);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-menu.get-tokens($theme), density);
  }

  @include token-utils.values($tokens);
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: menu,
      tokens: token-utils.get-overrides(m3-menu.get-tokens(), menu)
    ),
  );
}

@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
