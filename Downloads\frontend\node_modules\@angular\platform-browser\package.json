{"name": "@angular/platform-browser", "version": "18.2.11", "description": "Angular - library for using Angular in a web browser", "author": "angular", "license": "MIT", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/animations": "18.2.11", "@angular/core": "18.2.11", "@angular/common": "18.2.11"}, "peerDependenciesMeta": {"@angular/animations": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/platform-browser"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/platform-browser.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/platform-browser.mjs", "esm": "./esm2022/platform-browser.mjs", "default": "./fesm2022/platform-browser.mjs"}, "./animations": {"types": "./animations/index.d.ts", "esm2022": "./esm2022/animations/animations.mjs", "esm": "./esm2022/animations/animations.mjs", "default": "./fesm2022/animations.mjs"}, "./animations/async": {"types": "./animations/async/index.d.ts", "esm2022": "./esm2022/animations/async/async.mjs", "esm": "./esm2022/animations/async/async.mjs", "default": "./fesm2022/animations/async.mjs"}, "./testing": {"types": "./testing/index.d.ts", "esm2022": "./esm2022/testing/testing.mjs", "esm": "./esm2022/testing/testing.mjs", "default": "./fesm2022/testing.mjs"}}}