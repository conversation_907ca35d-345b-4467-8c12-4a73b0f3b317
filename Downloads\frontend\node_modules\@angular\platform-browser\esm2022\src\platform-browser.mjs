/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { bootstrapApplication, BrowserModule, createApplication, platformBrowser, provideProtractorTestingSupport, } from './browser';
export { Meta } from './browser/meta';
export { Title } from './browser/title';
export { disableDebugTools, enableDebugTools } from './browser/tools/tools';
export { By } from './dom/debug/by';
export { REMOVE_STYLES_ON_COMPONENT_DESTROY } from './dom/dom_renderer';
export { EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin } from './dom/events/event_manager';
export { HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, } from './dom/events/hammer_gestures';
export { DomSanitizer, } from './security/dom_sanitization_service';
export { HydrationFeatureKind, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withNoHttpTransferCache, } from './hydration';
export * from './private_export';
export { VERSION } from './version';
//# sourceMappingURL=data:application/json;base64,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