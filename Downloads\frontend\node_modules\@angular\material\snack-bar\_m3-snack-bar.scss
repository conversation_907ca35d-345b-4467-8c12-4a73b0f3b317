@use 'sass:map';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-snack-bar.
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  @return (
    base: (
      snack-bar-container-shape: map.get($system, corner-extra-small),
    ),
    color: (
      snack-bar-button-color: map.get($system, inverse-primary),
      snack-bar-container-color: map.get($system, inverse-surface),
      snack-bar-supporting-text-color: map.get($system, inverse-on-surface),
    ),
    typography: (
      snack-bar-supporting-text-font: map.get($system, body-medium-font),
      snack-bar-supporting-text-line-height: map.get($system, body-medium-line-height),
      snack-bar-supporting-text-size: map.get($system, body-medium-size),
      snack-bar-supporting-text-weight: map.get($system, body-medium-weight),
    ),
    density: (),
  );
}
