/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { from, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
/**
 * Perform a side effect through a switchMap for every emission on the source Observable,
 * but return an Observable that is identical to the source. It's essentially the same as
 * the `tap` operator, but if the side effectful `next` function returns an ObservableInput,
 * it will wait before continuing with the original value.
 */
export function switchTap(next) {
    return switchMap((v) => {
        const nextResult = next(v);
        if (nextResult) {
            return from(nextResult).pipe(map(() => v));
        }
        return of(v);
    });
}
//# sourceMappingURL=data:application/json;base64,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