{"name": "@angular-devkit/architect", "version": "0.1802.11", "description": "Angular Build Facade", "experimental": true, "main": "src/index.js", "typings": "src/index.d.ts", "dependencies": {"@angular-devkit/core": "18.2.11", "rxjs": "7.8.1"}, "builders": "./builders/builders.json", "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "packageManager": "yarn@4.4.0", "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependenciesMeta": {"esbuild": {"built": true}, "puppeteer": {"built": true}}}