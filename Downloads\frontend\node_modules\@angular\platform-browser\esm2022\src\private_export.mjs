/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { ɵgetDOM } from '@angular/common';
export { initDomAdapter as ɵinitDomAdapter, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, } from './browser';
export { BrowserDomAdapter as ɵBrowserDomAdapter } from './browser/browser_adapter';
export { BrowserGetTestability as ɵBrowserGetTestability } from './browser/testability';
export { DomRendererFactory2 as ɵDomRendererFactory2 } from './dom/dom_renderer';
export { DomEventsPlugin as ɵDomEventsPlugin } from './dom/events/dom_events';
export { HammerGesturesPlugin as ɵHammerGesturesPlugin } from './dom/events/hammer_gestures';
export { KeyEventsPlugin as ɵKeyEventsPlugin } from './dom/events/key_events';
export { SharedStylesHost as ɵSharedStylesHost } from './dom/shared_styles_host';
export { DomSanitizerImpl as ɵDomSanitizerImpl } from './security/dom_sanitization_service';
//# sourceMappingURL=data:application/json;base64,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