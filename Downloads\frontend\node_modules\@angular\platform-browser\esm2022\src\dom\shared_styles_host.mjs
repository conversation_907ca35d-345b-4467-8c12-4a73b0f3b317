/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { DOCUMENT, isPlatformServer } from '@angular/common';
import { APP_ID, CSP_NONCE, Inject, Injectable, Optional, PLATFORM_ID, } from '@angular/core';
import * as i0 from "@angular/core";
/** The style elements attribute name used to set value of `APP_ID` token. */
const APP_ID_ATTRIBUTE_NAME = 'ng-app-id';
export class SharedStylesHost {
    constructor(doc, appId, nonce, platformId = {}) {
        this.doc = doc;
        this.appId = appId;
        this.nonce = nonce;
        this.platformId = platformId;
        // Maps all registered host nodes to a list of style nodes that have been added to the host node.
        this.styleRef = new Map();
        this.hostNodes = new Set();
        this.styleNodesInDOM = this.collectServerRenderedStyles();
        this.platformIsServer = isPlatformServer(platformId);
        this.resetHostNodes();
    }
    addStyles(styles) {
        for (const style of styles) {
            const usageCount = this.changeUsageCount(style, 1);
            if (usageCount === 1) {
                this.onStyleAdded(style);
            }
        }
    }
    removeStyles(styles) {
        for (const style of styles) {
            const usageCount = this.changeUsageCount(style, -1);
            if (usageCount <= 0) {
                this.onStyleRemoved(style);
            }
        }
    }
    ngOnDestroy() {
        const styleNodesInDOM = this.styleNodesInDOM;
        if (styleNodesInDOM) {
            styleNodesInDOM.forEach((node) => node.remove());
            styleNodesInDOM.clear();
        }
        for (const style of this.getAllStyles()) {
            this.onStyleRemoved(style);
        }
        this.resetHostNodes();
    }
    addHost(hostNode) {
        this.hostNodes.add(hostNode);
        for (const style of this.getAllStyles()) {
            this.addStyleToHost(hostNode, style);
        }
    }
    removeHost(hostNode) {
        this.hostNodes.delete(hostNode);
    }
    getAllStyles() {
        return this.styleRef.keys();
    }
    onStyleAdded(style) {
        for (const host of this.hostNodes) {
            this.addStyleToHost(host, style);
        }
    }
    onStyleRemoved(style) {
        const styleRef = this.styleRef;
        styleRef.get(style)?.elements?.forEach((node) => node.remove());
        styleRef.delete(style);
    }
    collectServerRenderedStyles() {
        const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}="${this.appId}"]`);
        if (styles?.length) {
            const styleMap = new Map();
            styles.forEach((style) => {
                if (style.textContent != null) {
                    styleMap.set(style.textContent, style);
                }
            });
            return styleMap;
        }
        return null;
    }
    changeUsageCount(style, delta) {
        const map = this.styleRef;
        if (map.has(style)) {
            const styleRefValue = map.get(style);
            styleRefValue.usage += delta;
            return styleRefValue.usage;
        }
        map.set(style, { usage: delta, elements: [] });
        return delta;
    }
    getStyleElement(host, style) {
        const styleNodesInDOM = this.styleNodesInDOM;
        const styleEl = styleNodesInDOM?.get(style);
        if (styleEl?.parentNode === host) {
            // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.
            styleNodesInDOM.delete(style);
            styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);
            if (typeof ngDevMode === 'undefined' || ngDevMode) {
                // This attribute is solely used for debugging purposes.
                styleEl.setAttribute('ng-style-reused', '');
            }
            return styleEl;
        }
        else {
            const styleEl = this.doc.createElement('style');
            if (this.nonce) {
                styleEl.setAttribute('nonce', this.nonce);
            }
            styleEl.textContent = style;
            if (this.platformIsServer) {
                styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);
            }
            host.appendChild(styleEl);
            return styleEl;
        }
    }
    addStyleToHost(host, style) {
        const styleEl = this.getStyleElement(host, style);
        const styleRef = this.styleRef;
        const styleElRef = styleRef.get(style)?.elements;
        if (styleElRef) {
            styleElRef.push(styleEl);
        }
        else {
            styleRef.set(style, { elements: [styleEl], usage: 1 });
        }
    }
    resetHostNodes() {
        const hostNodes = this.hostNodes;
        hostNodes.clear();
        // Re-add the head element back since this is the default host.
        hostNodes.add(this.doc.head);
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.11", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }
    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "18.2.11", ngImport: i0, type: SharedStylesHost }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.11", ngImport: i0, type: SharedStylesHost, decorators: [{
            type: Injectable
        }], ctorParameters: () => [{ type: Document, decorators: [{
                    type: Inject,
                    args: [DOCUMENT]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [APP_ID]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [CSP_NONCE]
                }, {
                    type: Optional
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [PLATFORM_ID]
                }] }] });
//# sourceMappingURL=data:application/json;base64,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