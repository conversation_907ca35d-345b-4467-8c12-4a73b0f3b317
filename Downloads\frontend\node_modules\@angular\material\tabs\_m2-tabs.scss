@use 'sass:map';
@use '../core/theming/theming';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -4);

  @return (
    base: (
      // This is specified both here and in the density tokens, because it determines the size of
      // the tab itself and there are internal tests who don't configure the theme correctly.
      tab-container-height: 48px,
      tab-divider-color: transparent,
      tab-divider-height: 0,
      tab-active-indicator-height: 2px,
      tab-active-indicator-shape: 0,
    ),
    color: private-get-color-palette-color-tokens($theme, primary),
    typography: (
      tab-label-text-font: map.get($system, label-small-font),
      tab-label-text-size: map.get($system, label-small-size),
      tab-label-text-tracking: map.get($system, label-small-tracking),
      tab-label-text-line-height: map.get($system, label-small-line-height),
      tab-label-text-weight: map.get($system, label-small-weight),
    ),
    density: (
      tab-container-height: map.get((
        0: 48px,
        -1: 44px,
        -2: 40px,
        -3: 36px,
        -4: 32px,
      ), $density-scale),
    ),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant, $exclude: ()) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);

  $tokens: (
    tab-disabled-ripple-color: map.get($system, on-surface-variant),
    tab-pagination-icon-color: map.get($system, on-surface),
    tab-inactive-label-text-color: map.get($system, on-surface-variant),
    tab-active-label-text-color: map.get($system, primary),
    tab-active-ripple-color: map.get($system, primary),
    tab-inactive-ripple-color: map.get($system, primary),
    tab-inactive-focus-label-text-color: map.get($system, on-surface-variant),
    tab-inactive-hover-label-text-color: map.get($system, on-surface-variant),
    tab-active-focus-label-text-color: map.get($system, primary),
    tab-active-hover-label-text-color: map.get($system, primary),
    tab-active-focus-indicator-color: map.get($system, primary),
    tab-active-hover-indicator-color: map.get($system, primary),
    tab-active-indicator-color: map.get($system, primary),
    tab-background-color: map.get($system, primary),
    tab-foreground-color: map.get($system, on-primary),
  );

  @each $token in $exclude {
    $tokens: map.remove($tokens, $token);
  }

  @return $tokens;
}
