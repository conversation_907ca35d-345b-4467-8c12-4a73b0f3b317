/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { runInInjectionContext } from '@angular/core';
import { EMPTY, from, of, throwError } from 'rxjs';
import { catchError, concatMap, first, map, mapTo, mergeMap, takeLast, tap } from 'rxjs/operators';
import { RedirectCommand } from '../models';
import { getInherited, hasStaticTitle, } from '../router_state';
import { RouteTitleKey } from '../shared';
import { getDataKeys, wrapIntoObservable } from '../utils/collection';
import { getClosestRouteInjector } from '../utils/config';
import { getTokenOrFunctionIdentity } from '../utils/preactivation';
import { isEmptyError } from '../utils/type_guards';
import { redirectingNavigationError } from '../navigation_canceling_error';
import { DefaultUrlSerializer } from '../url_tree';
export function resolveData(paramsInheritanceStrategy, injector) {
    return mergeMap((t) => {
        const { targetSnapshot, guards: { canActivateChecks }, } = t;
        if (!canActivateChecks.length) {
            return of(t);
        }
        // Iterating a Set in javascript  happens in insertion order so it is safe to use a `Set` to
        // preserve the correct order that the resolvers should run in.
        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set#description
        const routesWithResolversToRun = new Set(canActivateChecks.map((check) => check.route));
        const routesNeedingDataUpdates = new Set();
        for (const route of routesWithResolversToRun) {
            if (routesNeedingDataUpdates.has(route)) {
                continue;
            }
            // All children under the route with a resolver to run need to recompute inherited data.
            for (const newRoute of flattenRouteTree(route)) {
                routesNeedingDataUpdates.add(newRoute);
            }
        }
        let routesProcessed = 0;
        return from(routesNeedingDataUpdates).pipe(concatMap((route) => {
            if (routesWithResolversToRun.has(route)) {
                return runResolve(route, targetSnapshot, paramsInheritanceStrategy, injector);
            }
            else {
                route.data = getInherited(route, route.parent, paramsInheritanceStrategy).resolve;
                return of(void 0);
            }
        }), tap(() => routesProcessed++), takeLast(1), mergeMap((_) => (routesProcessed === routesNeedingDataUpdates.size ? of(t) : EMPTY)));
    });
}
/**
 *  Returns the `ActivatedRouteSnapshot` tree as an array, using DFS to traverse the route tree.
 */
function flattenRouteTree(route) {
    const descendants = route.children.map((child) => flattenRouteTree(child)).flat();
    return [route, ...descendants];
}
function runResolve(futureARS, futureRSS, paramsInheritanceStrategy, injector) {
    const config = futureARS.routeConfig;
    const resolve = futureARS._resolve;
    if (config?.title !== undefined && !hasStaticTitle(config)) {
        resolve[RouteTitleKey] = config.title;
    }
    return resolveNode(resolve, futureARS, futureRSS, injector).pipe(map((resolvedData) => {
        futureARS._resolvedData = resolvedData;
        futureARS.data = getInherited(futureARS, futureARS.parent, paramsInheritanceStrategy).resolve;
        return null;
    }));
}
function resolveNode(resolve, futureARS, futureRSS, injector) {
    const keys = getDataKeys(resolve);
    if (keys.length === 0) {
        return of({});
    }
    const data = {};
    return from(keys).pipe(mergeMap((key) => getResolver(resolve[key], futureARS, futureRSS, injector).pipe(first(), tap((value) => {
        if (value instanceof RedirectCommand) {
            throw redirectingNavigationError(new DefaultUrlSerializer(), value);
        }
        data[key] = value;
    }))), takeLast(1), mapTo(data), catchError((e) => (isEmptyError(e) ? EMPTY : throwError(e))));
}
function getResolver(injectionToken, futureARS, futureRSS, injector) {
    const closestInjector = getClosestRouteInjector(futureARS) ?? injector;
    const resolver = getTokenOrFunctionIdentity(injectionToken, closestInjector);
    const resolverValue = resolver.resolve
        ? resolver.resolve(futureARS, futureRSS)
        : runInInjectionContext(closestInjector, () => resolver(futureARS, futureRSS));
    return wrapIntoObservable(resolverValue);
}
//# sourceMappingURL=data:application/json;base64,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