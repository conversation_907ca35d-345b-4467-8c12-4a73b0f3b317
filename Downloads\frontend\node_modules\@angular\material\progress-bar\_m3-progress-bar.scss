@use 'sass:map';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, progress-bar);

/// Generates the tokens for MDC linear-progress
/// @param {String} $color-variant The color variant to use for the component
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  @if $color-variant {
    $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
  }

  @return (
    base: (
      progress-bar-active-indicator-height: 4px,
      progress-bar-track-height: 4px,
      progress-bar-track-shape: map.get($system, corner-none)
    ),
    color: (
      progress-bar-active-indicator-color: map.get($system, primary),
      progress-bar-track-color: map.get($system, surface-variant),
    ),
    typography: (),
    density: (),
  );
}
