/**
 * @license Angular v18.2.11
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */


import { EnvironmentProviders } from '@angular/core';
import * as i0 from '@angular/core';
import { InjectionToken } from '@angular/core';
import { NgZone } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Renderer2 } from '@angular/core';
import { RendererFactory2 } from '@angular/core';
import { RendererType2 } from '@angular/core';
import { ɵAnimationEngine } from '@angular/animations/browser';
import { ɵAnimationRendererFactory } from '@angular/animations/browser';

/**
 * Returns the set of dependency-injection providers
 * to enable animations in an application. See [animations guide](guide/animations)
 * to learn more about animations in Angular.
 *
 * When you use this function instead of the eager `provideAnimations()`, animations won't be
 * rendered until the renderer is loaded.
 *
 * @usageNotes
 *
 * The function is useful when you want to enable animations in an application
 * bootstrapped using the `bootstrapApplication` function. In this scenario there
 * is no need to import the `BrowserAnimationsModule` NgModule at all, just add
 * providers returned by this function to the `providers` list as show below.
 *
 * ```typescript
 * bootstrapApplication(RootComponent, {
 *   providers: [
 *     provideAnimationsAsync()
 *   ]
 * });
 * ```
 *
 * @param type pass `'noop'` as argument to disable animations.
 *
 * @publicApi
 */
export declare function provideAnimationsAsync(type?: 'animations' | 'noop'): EnvironmentProviders;

/**
 * Provides a custom scheduler function for the async loading of the animation package.
 *
 * Private token for investigation purposes
 */
export declare const ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN: InjectionToken<(<T>(loadFn: () => T) => T)>;

export declare class ɵAsyncAnimationRendererFactory implements OnDestroy, RendererFactory2 {
    private doc;
    private delegate;
    private zone;
    private animationType;
    private moduleImpl?;
    private _rendererFactoryPromise;
    private readonly scheduler;
    private readonly loadingSchedulerFn;
    private _engine?;
    /**
     *
     * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)
     */
    constructor(doc: Document, delegate: RendererFactory2, zone: NgZone, animationType: 'animations' | 'noop', moduleImpl?: Promise<{
        ɵcreateEngine: (type: "animations" | "noop", doc: Document) => ɵAnimationEngine;
        ɵAnimationRendererFactory: typeof ɵAnimationRendererFactory;
    }> | undefined);
    /** @nodoc */
    ngOnDestroy(): void;
    /**
     * This method is delegating the renderer creation to the factories.
     * It uses default factory while the animation factory isn't loaded
     * and will rely on the animation factory once it is loaded.
     *
     * Calling this method will trigger as side effect the loading of the animation module
     * if the renderered component uses animations.
     */
    createRenderer(hostElement: any, rendererType: RendererType2): Renderer2;
    begin(): void;
    end(): void;
    whenRenderingDone?(): Promise<any>;
    static ɵfac: i0.ɵɵFactoryDeclaration<ɵAsyncAnimationRendererFactory, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ɵAsyncAnimationRendererFactory>;
}

export { }
