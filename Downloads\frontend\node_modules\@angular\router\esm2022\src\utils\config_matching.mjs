/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { of } from 'rxjs';
import { map } from 'rxjs/operators';
import { runCanMatchGuards } from '../operators/check_guards';
import { defaultUrlMatcher, PRIMARY_OUTLET } from '../shared';
import { UrlSegmentGroup } from '../url_tree';
import { last } from './collection';
import { getOrCreateRouteInjectorIfNeeded, getOutlet } from './config';
const noMatch = {
    matched: false,
    consumedSegments: [],
    remainingSegments: [],
    parameters: {},
    positionalParamSegments: {},
};
export function matchWithChecks(segmentGroup, route, segments, injector, urlSerializer) {
    const result = match(segmentGroup, route, segments);
    if (!result.matched) {
        return of(result);
    }
    // Only create the Route's `EnvironmentInjector` if it matches the attempted
    // navigation
    injector = getOrCreateRouteInjectorIfNeeded(route, injector);
    return runCanMatchGuards(injector, route, segments, urlSerializer).pipe(map((v) => (v === true ? result : { ...noMatch })));
}
export function match(segmentGroup, route, segments) {
    if (route.path === '**') {
        return createWildcardMatchResult(segments);
    }
    if (route.path === '') {
        if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {
            return { ...noMatch };
        }
        return {
            matched: true,
            consumedSegments: [],
            remainingSegments: segments,
            parameters: {},
            positionalParamSegments: {},
        };
    }
    const matcher = route.matcher || defaultUrlMatcher;
    const res = matcher(segments, segmentGroup, route);
    if (!res)
        return { ...noMatch };
    const posParams = {};
    Object.entries(res.posParams ?? {}).forEach(([k, v]) => {
        posParams[k] = v.path;
    });
    const parameters = res.consumed.length > 0
        ? { ...posParams, ...res.consumed[res.consumed.length - 1].parameters }
        : posParams;
    return {
        matched: true,
        consumedSegments: res.consumed,
        remainingSegments: segments.slice(res.consumed.length),
        // TODO(atscott): investigate combining parameters and positionalParamSegments
        parameters,
        positionalParamSegments: res.posParams ?? {},
    };
}
function createWildcardMatchResult(segments) {
    return {
        matched: true,
        parameters: segments.length > 0 ? last(segments).parameters : {},
        consumedSegments: segments,
        remainingSegments: [],
        positionalParamSegments: {},
    };
}
export function split(segmentGroup, consumedSegments, slicedSegments, config) {
    if (slicedSegments.length > 0 &&
        containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {
        const s = new UrlSegmentGroup(consumedSegments, createChildrenForEmptyPaths(config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));
        return { segmentGroup: s, slicedSegments: [] };
    }
    if (slicedSegments.length === 0 &&
        containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {
        const s = new UrlSegmentGroup(segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, config, segmentGroup.children));
        return { segmentGroup: s, slicedSegments };
    }
    const s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);
    return { segmentGroup: s, slicedSegments };
}
function addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, routes, children) {
    const res = {};
    for (const r of routes) {
        if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {
            const s = new UrlSegmentGroup([], {});
            res[getOutlet(r)] = s;
        }
    }
    return { ...children, ...res };
}
function createChildrenForEmptyPaths(routes, primarySegment) {
    const res = {};
    res[PRIMARY_OUTLET] = primarySegment;
    for (const r of routes) {
        if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {
            const s = new UrlSegmentGroup([], {});
            res[getOutlet(r)] = s;
        }
    }
    return res;
}
function containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, routes) {
    return routes.some((r) => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);
}
function containsEmptyPathMatches(segmentGroup, slicedSegments, routes) {
    return routes.some((r) => emptyPathMatch(segmentGroup, slicedSegments, r));
}
export function emptyPathMatch(segmentGroup, slicedSegments, r) {
    if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {
        return false;
    }
    return r.path === '';
}
export function noLeftoversInUrl(segmentGroup, segments, outlet) {
    return segments.length === 0 && !segmentGroup.children[outlet];
}
//# sourceMappingURL=data:application/json;base64,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