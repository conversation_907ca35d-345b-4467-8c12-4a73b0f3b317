/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { inject } from '@angular/core';
/**
 * Maps an array of injectable classes with canMatch functions to an array of equivalent
 * `CanMatchFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanMatch(providers) {
    return providers.map((provider) => (...params) => inject(provider).canMatch(...params));
}
/**
 * Maps an array of injectable classes with canActivate functions to an array of equivalent
 * `CanActivateFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanActivate(providers) {
    return providers.map((provider) => (...params) => inject(provider).canActivate(...params));
}
/**
 * Maps an array of injectable classes with canActivateChild functions to an array of equivalent
 * `CanActivateChildFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanActivateChild(providers) {
    return providers.map((provider) => (...params) => inject(provider).canActivateChild(...params));
}
/**
 * Maps an array of injectable classes with canDeactivate functions to an array of equivalent
 * `CanDeactivateFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanDeactivate(providers) {
    return providers.map((provider) => (...params) => inject(provider).canDeactivate(...params));
}
/**
 * Maps an injectable class with a resolve function to an equivalent `ResolveFn`
 * for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='Resolve'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToResolve(provider) {
    return (...params) => inject(provider).resolve(...params);
}
//# sourceMappingURL=data:application/json;base64,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